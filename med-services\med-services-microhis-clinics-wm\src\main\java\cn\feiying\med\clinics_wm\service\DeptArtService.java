package cn.feiying.med.clinics_wm.service;

import cn.feiying.med.clinics_wm.dto.DeptArtDto;
import cn.feiying.med.clinics_wm.dto.DeptStockDto;
import cn.feiying.med.clinics_wm.entity.ArtBatchEntity;
import cn.feiying.med.clinics_wm.entity.WmCountDetailEntity;
import cn.feiying.med.clinics_wm.model.ArtStockReserveResult;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.clinics_wm.entity.DeptArtEntity;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 仓库商品库存表
 *
 * <AUTHOR> @email
 * @date 2024-06-04 18:48:02
 * 2024-06-04 18:48:02
 */
public interface DeptArtService extends IService<DeptArtEntity> {

    PageUtils queryPage(long orgId, String deptCode, Map<String, Object> params);

    List<DeptArtDto> queryList(long orgId, String deptCode);

    List<DeptArtDto> queryList(long orgId, String deptCode, Map<String, Object> params);

    /**
     * 根据条件查询
     * 可作为公共方法使用，有传条件才查，不传条件不查
     * @param orgId
     * @param params
     * @return
     */
    List<DeptArtDto> findListByWhere(long orgId, Map<String, Object> params);

    DeptArtEntity findById(Long orgId, String deptCode, Long artId);

    DeptArtDto findDtoById(long orgId, String deptCode, Long artId);

    void updateEntity(long orgId, DeptArtEntity params);

    void purchaseIn(Long orgId, String deptCode, Long artId, BigDecimal totalCells, Integer splittable, DeptArtEntity initDeptArt);

    /**
     * 增加科室商品库存
     * @param orgId
     * @param deptCode
     * @param artId
     * @param totalPacks
     * @param totalCells
     * @param initDeptArt
     */
    void stockInc(Long orgId, String deptCode, Long artId, Integer totalPacks, BigDecimal totalCells, DeptArtEntity initDeptArt);

//    /**
//     * 增加科室商品批次库存
//     *
//     * @param orgId
//     * @param deptCode
//     * @param artId
//     * @param stockNo
//     * @param totalPacks
//     * @param totalCells
//     * @param initDeptStock
//     */
//    ArtStockChangeResult batchStockInc(Long orgId, String deptCode, Long artId, Integer stockNo, Integer totalPacks, BigDecimal totalCells, DeptStockEntity initDeptStock);

    /**
     * 减少科室商品库存
     *
     * @param orgId
     * @param deptCode
     * @param artId
     * @param packCells
     * @param totalPacks
     * @param totalCells
     * @param checkSplittable 是否校验拆零
     * @param initDeptArt
     */
    void stockDec(Long orgId, String deptCode, Long artId, Integer packCells, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable, DeptArtEntity initDeptArt);

    /**
     * 减少科室库存商品
     * @param orgId
     * @param deptCode
     * @param articleEntity
     * @param totalPacks
     * @param totalCells
     * @param checkSplittable
     * @return
     */
    int stockDecCount(Long orgId, String deptCode, ArticleEntity articleEntity, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable);

    /**
     * 减少科室商品批次库存
     * @param orgId
     * @param deptCode
     * @param articleEntity
     * @param batchNo       批号
     * @param stockNo       批次号
     * @param totalPacks
     * @param totalCells
     * @return
     */
    ArtStockReserveResult batchStockDec(Long orgId, String deptCode, ArticleEntity articleEntity, @Nullable String batchNo, @Nullable Integer stockNo, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable);

    /**
     * 计算请货量
     * @param orgId
     * @param deptCode
     * @param type
     * @param days
     * @return
     */
    List<DeptArtDto> calcReqList(long orgId, String deptCode, Integer type, Integer days);

    List<DeptStockDto> calcReqListByWbSeqid(long orgId, String deptCode, Long wbSeqid);

    /**
     * 获取指定商品在仓库中的批号列表
     * @param orgId
     * @param deptCode
     * @param artId
     * @return
     */
    List<ArtBatchEntity> getArtBatchNoList(long orgId, String deptCode, Long artId);

    /**
     * 查盘点单所有的批次商品信息
     * @param orgId
     * @param deptCode
     * @param countId
     * @return
     */
    List<WmCountDetailEntity> getArtBatchNoListByCountId(long orgId, String deptCode, Long countId);


    void addReservedCells(Long orgId, String deptCode, Long artId, BigDecimal reservedCells);

    void subReservedCells(Long orgId, String deptCode, Long artId, BigDecimal reservedCells);

    List<DeptArtDto> queryAllOrgDeptArtList(Map<String, Object> params);
    void remakeDeptArt(Long orgId, String deptCode, Long artId);

}


