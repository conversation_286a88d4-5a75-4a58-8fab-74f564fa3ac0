package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.GQueryWrapper;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.Query;
import cn.feiying.med.hip.enums.ClinicType;
import cn.feiying.med.hip.enums.RegAcctActionType;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.microhis.hsd.dao.RegAcctDao;
import cn.feiying.med.microhis.hsd.entity.RegAcctEntity;
import cn.feiying.med.microhis.hsd.entity.RegFeeEntity;
import cn.feiying.med.microhis.hsd.service.RegAcctService;
import cn.feiying.med.microhis.hsd.service.RegFeeService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 挂号收费记录表
 *
 * <AUTHOR> 2023-09-21 15:15:54
 */
@Slf4j
@Service("regAcctService")
public class RegAcctServiceImpl extends ServiceImpl<RegAcctDao, RegAcctEntity> implements RegAcctService {

    @Resource
    private ClinicianService clinicianService;
    @Resource
    private ClinicianDeptService clinicianDeptService;
    @Resource
    private OrgDeptService orgDeptService;
    @Resource
    private OrgItemPriceService orgItemPriceService;
    @Resource
    private RegFeeService regFeeService;
    @Resource
    private DeptQualRegService deptQualRegService;
    @Resource
    private OrgRegTypeService orgRegTypeService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<RegAcctEntity> wrapper = new GQueryWrapper<RegAcctEntity>().getWrapper(params);
        IPage<RegAcctEntity> page = this.page(new Query<RegAcctEntity>().getPage(params), wrapper);

        return new PageUtils(page);
    }

    @Override
    @Transactional
    public void saveEntity(RegAcctEntity entity) {
        save(entity);
    }

    @Override
    public List<RegFeeEntity> getRegAccLs(Long regId, Integer regTypeId, Long orgId, Long clinicianId, Integer clinicTypeId,
                                          String deptCode, boolean isChild, boolean isAged, boolean isEmergency) {
        return buildRegFeeLs(regId, regTypeId, orgId, clinicianId, clinicTypeId, deptCode, isChild, isAged, isEmergency);
    }

    @Override
    @Transactional
    public List<RegFeeEntity> saveRegAcc(Long regId, Integer regTypeId, Long orgId, Long userId, Long clinicianId, Integer clinicTypeId, String deptCode,
                                         Integer clinicDate, boolean isChild, boolean isAged, boolean isEmergency, RegAcctActionType actionType) {
        // 挂号费用列表
        List<RegFeeEntity> regFeeLs = buildRegFeeLs(regId, regTypeId, orgId, clinicianId, clinicTypeId, deptCode, isChild, isAged, isEmergency);
        // 保存挂号收费记录
        BigDecimal amount = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(regFeeLs)) {
            // 挂号金额
            amount = regFeeLs.stream().map(RegFeeEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 保存挂号计价明细
            regFeeService.saveBatchRegFee(regId, regFeeLs);
        }
        // 保存挂号收费记录
        remove(Wrappers.<RegAcctEntity>lambdaQuery().eq(RegAcctEntity::getRegId, regId).eq(RegAcctEntity::getActionType, actionType.getValue()));
        RegAcctEntity regAcct = RegAcctEntity.builder()
            .regId(regId)
            .actionType(actionType.getValue())
            .orgId(orgId)
            .userId(userId)
            .accDate(clinicDate)
            .amount(amount)
            .notes(clinicDate + "挂号收费记录单。")
            .build();
        save(regAcct);
        return regFeeLs;
    }

    private List<RegFeeEntity> buildRegFeeLs(Long regId, Integer regTypeId, Long orgId, Long clinicianId, Integer clinicTypeId, String deptCode,
            boolean isChild, boolean isAged, boolean isEmergency) {
        List<RegFeeEntity> regFeeLs = new ArrayList<>();
        boolean isNormalReg = true;
        if (regTypeId != null) {
            // 非常规挂号
            OrgRegTypeEntity orgRegType = orgRegTypeService.findById(orgId, regTypeId);
            if (orgRegType != null && Convert.toInt(orgRegType.getIsUnconventional(), 0) == 1) {
                isNormalReg = false;
                BigDecimal unitPrice = BigDecimal.ZERO;
                if (orgRegType.getUnconventionalAmt() == null || orgRegType.getUnconventionalAmt().compareTo(BigDecimal.ZERO) < 0) {
                    OrgItemPriceEntity orgItemPrice = orgItemPriceService.getById(orgId, orgRegType.getArtId());
                    if (orgItemPrice != null && orgItemPrice.getUnitPrice() != null && orgItemPrice.getCheckedFlag() != null && orgItemPrice.getCheckedFlag() == 1) {
                        unitPrice = getUnitPrice(clinicTypeId, orgItemPrice);
                    }
                } else {
                    unitPrice = orgRegType.getUnconventionalAmt();
                }
                // 基础挂号费用
                RegFeeEntity baseRegFee = RegFeeEntity.builder()
                        .regId(regId)
                        .artId(orgRegType.getArtId())
                        .price(unitPrice)
                        .total(BigDecimal.ONE)
                        .amount(unitPrice)
                        .notes("基础挂号费用")
                        .build();
                regFeeLs.add(baseRegFee);
            }
        }
        // 正常挂号
        if (isNormalReg) {
            Long artId = null;
            OrgItemPriceEntity orgItemPrice = null;
            if (clinicianId != null) {
                // 获取医生科室信息
                ClinicianDeptEntity clinicianDept = clinicianDeptService.findById(orgId, clinicianId, deptCode);
                if (clinicianDept == null) {
                    throw new SaveFailureException("医生未设置科室");
                }
                artId = clinicianDept.getArtId();
                if (isEmergency && clinicianDept.getEmArtId() != null) {
                    artId = clinicianDept.getEmArtId();
                } else if (isChild && clinicianDept.getChildrenArtId() != null) {
                    artId = clinicianDept.getChildrenArtId();
                } else if (isAged && clinicianDept.getAgedArtId() != null) {
                    artId = clinicianDept.getAgedArtId();
                }
                if (artId != null) {
                    orgItemPrice = orgItemPriceService.getById(orgId, artId);
//                if (orgItemPrice == null || orgItemPrice.getUnitPrice() == null) {
//                    throw new SaveFailureException("医生执行科室挂号费未设置基础价格");
//                } else if (orgItemPrice.getCheckedFlag() == null || orgItemPrice.getCheckedFlag() == 0) {
//                    ArticleEntity article = articleService.getById(artId);
//                    throw new SaveFailureException(article.getArtName() + "机构物价未核对");
//                }
                }
            }
            // 医生未设置挂号条目按科室职称挂号
            if (orgItemPrice == null && clinicianId != null) {
                ClinicianEntity clinician = clinicianService.getById(clinicianId);
                if (clinician != null && clinician.getQualLevelId() != null) {
                    DeptQualRegEntity deptQualReg = deptQualRegService.getOne(Wrappers.lambdaQuery(DeptQualRegEntity.class)
                            .eq(DeptQualRegEntity::getOrgId, orgId).eq(DeptQualRegEntity::getDeptCode, deptCode)
                            .eq(DeptQualRegEntity::getQualLevelId, clinician.getQualLevelId()));
                    if (deptQualReg != null) {
                        artId = deptQualReg.getOpArtId();
                        if (isEmergency && deptQualReg.getEmArtId() != null) {
                            artId = deptQualReg.getEmArtId();
                        } else if (isChild && deptQualReg.getChildrenArtId() != null) {
                            artId = deptQualReg.getChildrenArtId();
                        } else if (isAged && deptQualReg.getAgedArtId() != null) {
                            artId = deptQualReg.getAgedArtId();
                        }
                        if (artId != null) {
                            orgItemPrice = orgItemPriceService.getById(orgId, artId);
                        }
                    }
                }
            }
            // 医生未设置挂号条目或按科室挂号
            OrgDeptEntity orgDept = orgDeptService.findById(orgId, deptCode);
            if (orgItemPrice == null) {
                artId = orgDept.getArtId();
                if (isEmergency && orgDept.getEmArtId() != null) {
                    artId = orgDept.getEmArtId();
                } else if (isChild && orgDept.getChildrenArtId() != null) {
                    artId = orgDept.getChildrenArtId();
                } else if (isAged && orgDept.getAgedArtId() != null) {
                    artId = orgDept.getAgedArtId();
                }
//            if (artId == null) {
//                throw new SaveFailureException("未设置机构科室" + (isEmergency ? "急诊挂号收费条目" : isChild ? "儿童挂号收费条目" : isAged ? "老人挂号收费条目" : "门诊挂号收费条目"));
//            }
                if (artId != null) {
                    orgItemPrice = orgItemPriceService.getById(orgId, artId);
//                if (orgItemPrice == null || orgItemPrice.getUnitPrice() == null) {
//                    throw new SaveFailureException("科室挂号费未设置基础价格");
//                } else if (orgItemPrice.getCheckedFlag() == null || orgItemPrice.getCheckedFlag() == 0) {
//                    ArticleEntity article = articleService.getById(artId);
//                    throw new SaveFailureException(article.getArtName() + "机构物价未核对");
//                }
                }
            }
            // 基础挂号费用
            RegFeeEntity baseRegFee = new RegFeeEntity();
            if (orgItemPrice != null && orgItemPrice.getUnitPrice() != null && orgItemPrice.getCheckedFlag() != null && orgItemPrice.getCheckedFlag() == 1) {
                BigDecimal unitPrice = getUnitPrice(clinicTypeId, orgItemPrice);
                baseRegFee = RegFeeEntity.builder()
                        .regId(regId)
                        .artId(artId)
                        .price(unitPrice)
                        .total(BigDecimal.ONE)
                        .amount(unitPrice)
                        .notes("基础挂号费用")
                        .build();
                regFeeLs.add(baseRegFee);
            }
            // 急诊加收费用
            if (isEmergency && orgDept.getEmAddArtId() != null) {
                BigDecimal emergencyAddRate = orgDept.getEmergencyAddRate() != null ? orgDept.getEmergencyAddRate() : BigDecimal.ZERO;
                BigDecimal emergencyAmount = (baseRegFee.getAmount() != null ? baseRegFee.getAmount() : BigDecimal.ZERO).multiply(emergencyAddRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP))
                        .add(orgDept.getEmergencyAddAmt() == null ? BigDecimal.ZERO : orgDept.getEmergencyAddAmt());
                if (emergencyAmount.compareTo(BigDecimal.ZERO) != 0) {
                    RegFeeEntity emergencyRegFee = RegFeeEntity.builder()
                            .regId(regId)
                            .artId(orgDept.getEmAddArtId())
                            .price(emergencyAmount)
                            .total(BigDecimal.ONE)
                            .amount(emergencyAmount)
                            .notes("急诊加收费用")
                            .build();
                    regFeeLs.add(emergencyRegFee);
                }
            }
            // 儿童加收费用
            if (isChild && orgDept.getChildrenAddArtId() != null) {
                BigDecimal childrenAddRate = orgDept.getChildrenAddRate() != null ? orgDept.getChildrenAddRate() : BigDecimal.ZERO;
                BigDecimal childrenAmount = (baseRegFee.getAmount() != null ? baseRegFee.getAmount() : BigDecimal.ZERO).multiply(childrenAddRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP))
                        .add(orgDept.getChildrenAddAmt() == null ? BigDecimal.ZERO : orgDept.getChildrenAddAmt());
                if (childrenAmount.compareTo(BigDecimal.ZERO) != 0) {
                    RegFeeEntity childrenRegFee = RegFeeEntity.builder()
                            .regId(regId)
                            .artId(orgDept.getChildrenAddArtId())
                            .price(childrenAmount)
                            .total(BigDecimal.ONE)
                            .amount(childrenAmount)
                            .notes("儿童加收费用")
                            .build();
                    regFeeLs.add(childrenRegFee);
                }
            }
            // 老年加收费用
            if (isAged && orgDept.getAgedAddArtId() != null) {
                BigDecimal agedAddRate = orgDept.getAgedAddRate() != null ? orgDept.getAgedAddRate() : BigDecimal.ZERO;
                BigDecimal agedAmount = (baseRegFee.getAmount() != null ? baseRegFee.getAmount() : BigDecimal.ZERO).multiply(agedAddRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP))
                        .add(orgDept.getAgedAddAmt() == null ? BigDecimal.ZERO : orgDept.getAgedAddAmt());
                if (agedAmount.compareTo(BigDecimal.ZERO) != 0) {
                    RegFeeEntity agedRegFee = RegFeeEntity.builder()
                            .regId(regId)
                            .artId(orgDept.getAgedAddArtId())
                            .price(agedAmount)
                            .total(BigDecimal.ONE)
                            .amount(agedAmount)
                            .notes("老年加收费用")
                            .build();
                    regFeeLs.add(agedRegFee);
                }
            }
        }
        return regFeeLs;
    }

    private BigDecimal getUnitPrice(Integer clinicTypeId, OrgItemPriceEntity orgItemPrice) {
        BigDecimal unitPrice = orgItemPrice.getUnitPrice();
        if (clinicTypeId != null) {
            if (clinicTypeId.equals(ClinicType.outpatient.getValue()) && orgItemPrice.getOpPrice() != null && orgItemPrice.getOpPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 门诊单价
                unitPrice = orgItemPrice.getOpPrice();
            } else if (clinicTypeId.equals(ClinicType.inpatient.getValue()) && orgItemPrice.getIpPrice() != null && orgItemPrice.getIpPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 住院单价
                unitPrice = orgItemPrice.getIpPrice();
            }
        }
        return unitPrice;
    }

    @Override
    @Transactional
    public void delete(Integer... idLs) {
        for(Integer id : idLs) {
            removeById(id);
        }
    }

    @Override
    public RegAcctEntity findById(Long regId, int actionType) {
        return getOne(new LambdaQueryWrapper<RegAcctEntity>()
                .eq(RegAcctEntity::getRegId, regId)
                .eq(RegAcctEntity::getActionType, actionType));
    }

    @Override
    public RegAcctEntity getByBillId(long billId, RegAcctActionType actionType) {
        return getOne(new LambdaQueryWrapper<RegAcctEntity>()
                .eq(RegAcctEntity::getBseqid, billId)
                .eq(RegAcctEntity::getActionType, actionType.getValue()));
    }

    @Override
    public RegAcctEntity get(long cashId, RegAcctActionType actionType) {
        return getOne(new LambdaQueryWrapper<RegAcctEntity>()
                .eq(RegAcctEntity::getCashId, cashId)
                .eq(RegAcctEntity::getActionType, actionType.getValue()));
    }

    @Override
    @Nullable
    public RegAcctEntity getByRegId(long regId, RegAcctActionType actionType) {
        return getOne(new LambdaQueryWrapper<RegAcctEntity>()
                .eq(RegAcctEntity::getRegId, regId)
                .eq(RegAcctEntity::getActionType, actionType.getValue()));

    }
}
