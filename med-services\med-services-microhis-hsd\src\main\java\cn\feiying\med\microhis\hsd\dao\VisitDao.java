package cn.feiying.med.microhis.hsd.dao;

import cn.feiying.med.microhis.hsd.dto.VisitDeptDto;
import cn.feiying.med.microhis.hsd.dto.VisitDto;
import cn.feiying.med.microhis.hsd.entity.RecipeEntity;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 诊疗记录表
 *
 * <AUTHOR> 2023-09-21 15:15:54
 */
@Mapper
public interface VisitDao extends BaseMapper<VisitEntity> {

    IPage<VisitDto> queryPage(IPage<VisitDto> page, @Param(Constants.WRAPPER) QueryWrapper<VisitDto> wrapper);

    List<VisitDto> queryPage(@Param(Constants.WRAPPER) QueryWrapper<VisitDto> wrapper);

    List<VisitDto> findOutpatientVisitLs(@Param(Constants.WRAPPER) QueryWrapper<VisitDto> wrapper);

    VisitDto findById(@Param("visitId") Long visitId);

    List<Map<String, Object>> findVisitStats(@Param(Constants.WRAPPER) QueryWrapper<VisitEntity> wrapper);

    Map<String, Object> findDisVisitStats(@Param(Constants.WRAPPER) QueryWrapper<VisitEntity> wrapper);

    Map<String, Object> findRecipeStats(@Param(Constants.WRAPPER) QueryWrapper<RecipeEntity> wrapper);

    @Select("select IFNULL(MAX(t_visit.Visit_No), 0) + 1 from microhis_hsd.t_visit where t_visit.Clinic_Date = #{clinicDate} and t_visit.Visit_Status = 2")
    Integer getVisitNo(@Param("clinicDate") Integer clinicDate);

    @Select("select IFNULL(MAX(t_visit.Visit_Times), 0) + 1 from microhis_hsd.t_visit where t_visit.Org_Id = #{orgId} and t_visit.Patient_ID = #{patientId} and t_visit.Visit_Status = 3 and t_visit.Clinic_Type_ID in (1, 3)")
    Integer getVisitTimes( @Param("orgId") Long orgId, @Param("patientId") Long patientId);
}
