import java.math.BigDecimal;

/**
 * 拆零盒整计算验证程序
 * 验证修正后的计算公式是否正确
 */
public class 拆零盒整计算验证 {
    
    public static void main(String[] args) {
        System.out.println("=== 拆零盒整计算验证 ===");
        
        // 测试数据
        Integer deptTotalPacks = 123;    // 仓库总库存整包数
        BigDecimal deptTotalCells = new BigDecimal("80");  // 仓库总库存拆零数
        Integer totalPacks = 23;         // 批次库存整包数
        BigDecimal totalCells = new BigDecimal("80");      // 批次库存拆零数
        Integer packCells = 100;         // 拆零系数
        
        System.out.println("输入数据：");
        System.out.println("仓库总库存：" + deptTotalPacks + "整包 + " + deptTotalCells + "拆零");
        System.out.println("批次库存：" + totalPacks + "整包 + " + totalCells + "拆零");
        System.out.println("拆零系数：" + packCells);
        System.out.println();
        
        // 执行计算
        PackAndCellCalculationResult result = calculatePackAndCellConversion(
            deptTotalPacks, deptTotalCells, totalPacks, totalCells, packCells);
        
        System.out.println("计算结果：");
        System.out.println("最终整包数：" + result.finalTotalPacks);
        System.out.println("最终拆零数：" + result.finalTotalCells);
        System.out.println();
        
        // 验证结果
        System.out.println("验证结果：");
        if (result.finalTotalPacks.equals(147) && result.finalTotalCells.equals(new BigDecimal("60"))) {
            System.out.println("✅ 计算结果正确！");
        } else {
            System.out.println("❌ 计算结果错误！");
            System.out.println("期望：147整包 + 60拆零");
            System.out.println("实际：" + result.finalTotalPacks + "整包 + " + result.finalTotalCells + "拆零");
        }
    }
    
    /**
     * 拆零盒整计算方法（修正后的版本）
     */
    private static PackAndCellCalculationResult calculatePackAndCellConversion(
            Integer deptTotalPacks, BigDecimal deptTotalCells, 
            Integer totalPacks, BigDecimal totalCells, Integer packCells) {
        
        // 初始化默认值
        if (deptTotalPacks == null) deptTotalPacks = 0;
        if (deptTotalCells == null) deptTotalCells = BigDecimal.ZERO;
        if (totalPacks == null) totalPacks = 0;
        if (totalCells == null) totalCells = BigDecimal.ZERO;
        if (packCells == null || packCells <= 0) packCells = 1;
        
        System.out.println("计算步骤：");
        
        // 1. 计算仓库总库存的总拆零数
        // 仓库总库存 = 整包数 * 整包单位 + 拆零数
        BigDecimal deptTotalInCells = BigDecimal.valueOf(deptTotalPacks)
            .multiply(BigDecimal.valueOf(packCells))
            .add(deptTotalCells);
        
        // 重新计算仓库总库存的盒整
        Integer deptNewPacks = deptTotalInCells.divide(
            BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue(); // 整包数量（取整）
        BigDecimal deptNewCells = deptTotalInCells.remainder(
            BigDecimal.valueOf(packCells)); // 剩余拆零数量（取余）
        
        System.out.println("1. 仓库总拆零数 = " + deptTotalPacks + " × " + packCells + " + " + deptTotalCells + " = " + deptTotalInCells);
        System.out.println("   仓库盒整后：" + deptNewPacks + "整包 + " + deptNewCells + "拆零");
        
        // 2. 计算批次库存的总拆零数
        // 批次库存 = 整包数 * 整包单位 + 拆零数
        BigDecimal batchTotalInCells = BigDecimal.valueOf(totalPacks)
            .multiply(BigDecimal.valueOf(packCells))
            .add(totalCells);
        
        // 重新计算批次库存的盒整
        Integer batchNewPacks = batchTotalInCells.divide(
            BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue(); // 整包数量（取整）
        BigDecimal batchNewCells = batchTotalInCells.remainder(
            BigDecimal.valueOf(packCells)); // 剩余拆零数量（取余）
        
        System.out.println("2. 批次总拆零数 = " + totalPacks + " × " + packCells + " + " + totalCells + " = " + batchTotalInCells);
        System.out.println("   批次盒整后：" + batchNewPacks + "整包 + " + batchNewCells + "拆零");
        
        // 计算最终结果（仓库库存 + 批次库存）
        Integer finalTotalPacks = deptNewPacks + batchNewPacks;
        BigDecimal finalTotalCells = deptNewCells.add(batchNewCells);
        
        System.out.println("3. 合并结果：" + deptNewPacks + " + " + batchNewPacks + " = " + finalTotalPacks + "整包");
        System.out.println("             " + deptNewCells + " + " + batchNewCells + " = " + finalTotalCells + "拆零");
        
        // 如果最终拆零数量超过拆零系数，需要再次进行盒整操作
        if (finalTotalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            Integer additionalPacks = finalTotalCells.divide(
                BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            BigDecimal remainingCells = finalTotalCells.remainder(BigDecimal.valueOf(packCells));
            
            System.out.println("4. 最终盒整：" + finalTotalCells + " ÷ " + packCells + " = " + additionalPacks + "整包 + " + remainingCells + "拆零");
            
            finalTotalPacks += additionalPacks;
            finalTotalCells = remainingCells;
            
            System.out.println("   最终结果：" + finalTotalPacks + "整包 + " + finalTotalCells + "拆零");
        }
        
        return new PackAndCellCalculationResult(finalTotalPacks, finalTotalCells);
    }
    
    /**
     * 计算结果类
     */
    static class PackAndCellCalculationResult {
        final Integer finalTotalPacks;
        final BigDecimal finalTotalCells;
        
        PackAndCellCalculationResult(Integer finalTotalPacks, BigDecimal finalTotalCells) {
            this.finalTotalPacks = finalTotalPacks;
            this.finalTotalCells = finalTotalCells;
        }
    }
}
