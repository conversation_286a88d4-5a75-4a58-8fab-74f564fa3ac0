package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.dao.DeptArtDao;
import cn.feiying.med.clinics_wm.dto.DeptArtDto;
import cn.feiying.med.clinics_wm.dto.DeptStockDto;
import cn.feiying.med.clinics_wm.entity.*;
import cn.feiying.med.clinics_wm.enums.ArtShortFlag;
import cn.feiying.med.clinics_wm.enums.ReservedFlag;
import cn.feiying.med.clinics_wm.model.ArtPriceModel;
import cn.feiying.med.clinics_wm.model.ArtStockChangeResult;
import cn.feiying.med.clinics_wm.model.ArtStockReserveResult;
import cn.feiying.med.clinics_wm.service.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.enums.ArtCatType;
import cn.feiying.med.hip.enums.ArtType;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.feiying.med.hip.model.hsd.SectionArtVo;
import cn.feiying.med.saas.api.service.RemoteInpatientHsdService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库商品库存表
 * <AUTHOR> 18:48:02
 */
@Slf4j
@Service("deptArtService")
public class DeptArtServiceImpl extends ServiceImpl<DeptArtDao, DeptArtEntity> implements DeptArtService {
    @Resource
    private DeptStockService deptStockService;
    @Resource
    private OrgArtService orgArtService;
    @Resource
    private OrgCustMapService orgCustMapService;
    @Resource
    private ArticleService articleService;
    @Resource
    private RemoteInpatientHsdService remoteInpatientHsdService;
    @Resource
    private ArtStocknoService artStocknoService;

    private QueryWrapper<DeptArtDto> buildQueryWrapper(long orgId, String deptCode, Map<String, Object> params) {
        QueryWrapper<DeptArtDto> wrapper = new GQueryWrapper<DeptArtDto>().getWrapper(params);
        wrapper.eq("t_dept_art.org_id", orgId);
        wrapper.eq("t_dept_art.dept_code", deptCode);
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        String artName = Convert.toStr(params.get("artName"), StrUtil.EMPTY).trim().toUpperCase();
        keyword = StrUtil.isNotBlank(keyword) ? keyword : artName;
        if (StringUtil.isNotEmpty(keyword)) {
            String finalKeyword = keyword;
            wrapper.and(p -> p.eq("t_article.Art_ID", finalKeyword)
                    .or().like("UPPER(t_article.Art_Code)", finalKeyword).or().like("UPPER(t_article.Art_Name)", finalKeyword)
                    .or().like("UPPER(t_article.QS_Code1)", finalKeyword).or().like("UPPER(t_article.QS_Code2)", finalKeyword)
                    .or().like("UPPER(t_article.CDAN_Name)", finalKeyword).or().like("UPPER(t_article.MI_Code)", finalKeyword)
                    .or().like("UPPER(t_article.YPID_Code)", finalKeyword));
        }
        Long countId = Convert.toLong(params.get("countId"));
        if (countId != null) {
            wrapper.eq("t_dept_art.count_id", countId);
        }
        if (params.containsKey("artIds")) {
            wrapper.in("t_article.Art_ID", Convert.toLongArray(params.get("artIds")));
        }
        List<Long> artIdLs = Convert.toList(Long.class, params.get("artIdLs"));
        if (ObjectUtil.isNotEmpty(artIdLs)) {
            wrapper.in("t_article.Art_ID", artIdLs);
        }

        Integer artCatId = Convert.toInt(params.get("artCatId"));
        if (artCatId != null) {
            wrapper.eq("t_article.Cat_Type_ID", artCatId);
        }
        Integer artSubTypeId = Convert.toInt(params.get("artSubTypeId"));
        if (artSubTypeId != null) {
            wrapper.eq("t_article.Subtype_ID", artSubTypeId);
        }

        Integer forOutpatient = Convert.toInt(params.get("forOutpatient"));
        if (forOutpatient != null) {
            wrapper.and(p -> p.isNull("t_org_formulary.For_Outpatient").or().eq("t_org_formulary.For_Outpatient", forOutpatient));
        }
        Integer forInpatient = Convert.toInt(params.get("forInpatient"));
        if (forInpatient != null) {
            wrapper.and(p -> p.isNull("t_org_formulary.For_Inpatient").or().eq("t_org_formulary.For_Inpatient", forInpatient));
        }
        Integer genderId = Convert.toInt(params.get("genderId"));
        if (genderId != null) {
            String sql = "(t_org_formulary.Allow_Gender is null OR t_org_formulary.Allow_Gender = 'A'";
            if (genderId == 1) {
                sql += " OR t_org_formulary.Allow_Gender = 'M'";
            } else if (genderId == 2) {
                sql += " OR t_org_formulary.Allow_Gender = 'F'";
            }
            sql += ")";
            wrapper.apply(sql);
        }
        // 禁用状态
        Integer disabled = Convert.toInt(params.get("disabled"));
        if (disabled != null) {
            wrapper.apply("IFNULL(t_article.Disabled, 0) = {0}", disabled);
            wrapper.apply("IFNULL(t_org_formulary.Disabled, 0) = {0}", disabled);
        }
        // 查询是否在机构处方集里面
        Integer isFormulary = Convert.toInt(params.get("isFormulary"), 0);
        if (isFormulary == 1) {
            wrapper.eq("t_org_formulary.Org_ID", orgId);
        }
        String applyDeptCode = Convert.toStr(params.get("applyDeptCode"), StrUtil.EMPTY).trim();
        if (StringUtil.isNotEmpty(applyDeptCode)) {
            wrapper.apply("(t_org_formulary.Include_Depts is null OR CONCAT(',', t_org_formulary.Include_Depts, ',') REGEXP CONCAT(',', '" + applyDeptCode + "', ','))");
            wrapper.apply("(t_org_formulary.Exclude_Depts is null OR CONCAT(',', t_org_formulary.Exclude_Depts, ',') NOT REGEXP CONCAT(',', '" + applyDeptCode + "', ','))");
        }
        Integer forOe = Convert.toInt(params.get("forOe"));
        if (forOe != null) {
            wrapper.apply("IFNULL(t_article.For_Oe, 0) = {0}", forOe);
        }
        Integer forDrug = Convert.toInt(params.get("forDrug"));
        if (forDrug != null && forDrug == 1) {
            wrapper.and(p -> p.in("t_article.Art_Type_ID", ArtType.MoeDrug.getValue(), ArtType.MoeChineseDrug.getValue())
                    .or().eq("t_article.Cat_Type_ID", ArtCatType.CONTRACT_PRESCRIPTION.getValue()));
        }
        Integer hasStock = Convert.toInt(params.get("hasStock"), 0);
        if (hasStock == 1) {
            wrapper.apply("(ifnull(t_dept_art.Total_Packs, 0) <> 0 or ifnull(t_dept_art.Total_Cells,0) <> 0)");
        }
        Integer cellsTotalLe = Convert.toInt(params.get("cellsTotalLe"));
        if (cellsTotalLe != null) {
            wrapper.apply("ifnull(t_dept_art.Total_Packs, 0) * ifnull(t_article.Pack_Cells, 1) + ifnull(t_dept_art.Total_Cells, 0) <= {0}", cellsTotalLe);
        }
        return wrapper;
    }

    private void buildDtoList(List<DeptArtDto> list) {
        list.forEach(item -> {
            buildDto(item);
        });
    }

    private void buildDto(DeptArtDto item) {
        if (item.getPackCells() == null) {
            item.setPackCells(1);
        }
        if (item.getTotalPacks() == null) {
            item.setTotalPacks(0);
        }
        if (item.getTotalCells() == null) {
            item.setTotalCells(BigDecimal.ZERO);
        }
        if (item.getSafeCells() != null && !item.getSafeCells().equals(0)) {
            item.setSafeTotalPacks(BigDecimal.valueOf(item.getSafeCells()).divide(new BigDecimal(item.getPackCells()), 0, BigDecimal.ROUND_DOWN).intValue());
            item.setSafeTotalCells(item.getSafeCells() - (item.getSafeTotalPacks() * item.getPackCells()));
        }
        if (item.getLastRestCells() != null && !item.getLastRestCells().equals(0)) {
            item.setLastRestTotalPacks(BigDecimal.valueOf(item.getLastRestCells()).divide(new BigDecimal(item.getPackCells()), 0, BigDecimal.ROUND_DOWN).intValue());
            item.setLastRestTotalCells(item.getLastRestCells() - (item.getLastRestTotalPacks() * item.getPackCells()));
        }
        BigDecimal currentCells = BigDecimal.valueOf(item.getTotalPacks()).multiply(BigDecimal.valueOf(item.getPackCells()));
        if (item.getTotalCells() != null) {
            currentCells = currentCells.add(item.getTotalCells());
        }
        item.setCurrentCells(currentCells);
        if (item.getLastRestCells() != null && !item.getLastRestCells().equals(0)) {
            BigDecimal usedCells = BigDecimal.valueOf(item.getLastRestCells()).subtract(currentCells);
            if (usedCells.compareTo(BigDecimal.ZERO) > 0) {
                // 动销率=(申领入库后库存制剂数-当前库存数)/申领入库后库存制剂数 * 100%
                item.setDxl(usedCells.divide(BigDecimal.valueOf(item.getLastRestCells()), 2, BigDecimal.ROUND_DOWN).multiply(BigDecimal.valueOf(100)));
                // 日均消耗=(申领入库后库存制剂数-当前库存数)/(当前时间-最近申领入库时间的间隔天数)
                long days = DateUtil.betweenDay(new Date(), item.getLastSupplemented(), true);
                if (days <= 0) {
                    days = 1;
                }
                item.setUsedByDay(usedCells.divide(BigDecimal.valueOf(days), 2, BigDecimal.ROUND_DOWN));
                // 预计可用天数=当前库存数/日均消耗
                if (Convert.toBigDecimal(item.getUsedByDay(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0) {
                    item.setCanUseDays(currentCells.divide(item.getUsedByDay(), 0, BigDecimal.ROUND_DOWN).intValue());
                }
            }
        }
    }

    @Override
    public PageUtils queryPage(long orgId, String deptCode, Map<String, Object> params) {
        QueryWrapper<DeptArtDto> wrapper = buildQueryWrapper(orgId, deptCode, params);
        IPage<DeptArtDto> page = this.baseMapper.queryDtoPage(new Query<DeptArtDto>().getPage(params), wrapper);
        buildDtoList(page.getRecords());
        boolean showCost = Convert.toBool(params.get("showCost"), false);
        if (showCost) {
            setAvgPrice(orgId, deptCode, page.getRecords());
        }
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        if (sectionId != null && !page.getRecords().isEmpty()) {
            List<Long> artIds = page.getRecords().stream().map(p -> p.getArtId()).collect(Collectors.toList());
            List<SectionArtVo> sectionArtEntities = remoteInpatientHsdService.sectionArt(sectionId, artIds);
//            List<SectionArtEntity> sectionArtEntities = sectionArtService.list(new LambdaQueryWrapper<SectionArtEntity>()
//                    .eq(SectionArtEntity::getSectionId, sectionId)
//                    .in(SectionArtEntity::getArtId, artIds)
//            );
            page.getRecords().forEach(item -> {
                SectionArtVo sectionArtEntity = sectionArtEntities.stream().filter(p -> p.getArtId().equals(item.getArtId())).findFirst().orElse(null);
                if (sectionArtEntity != null) {
                    item.setSectionTotalPacks(sectionArtEntity.getTotalPacks());
                    item.setSectionTotalCells(sectionArtEntity.getTotalCells());
                }
            });
        }
        return new PageUtils(page);
    }

    private void setAvgPrice(long orgId, String deptCode, List<DeptArtDto> records) {
        if (ObjectUtil.isNotEmpty(records)) {
            List<Long> artIdLs = records.stream().map(DeptArtEntity::getArtId).distinct().collect(Collectors.toList());
            List<OrgArtEntity> orgArtLs = orgArtService.list(new LambdaQueryWrapper<OrgArtEntity>().eq(OrgArtEntity::getOrgId, orgId).in(OrgArtEntity::getArtId, artIdLs));
            List<DeptStockDto> deptStockLs = deptStockService.findListByArtIdLs(orgId, deptCode, artIdLs);
            OrgCustMapEntity orgCustMap = orgCustMapService.findById(orgId);
            records.forEach(deptArt -> {
                orgArtLs.stream().filter(orgArt -> orgArt.getArtId().equals(deptArt.getArtId())).findFirst().ifPresent(orgArt -> {
                    BigDecimal orgPackPrice = orgArt.getPackPrice();
                    BigDecimal lastBuyPrice = orgArt.getLastBuyPrice();
                    BigDecimal pctAdd = Convert.toBigDecimal(orgArt.getPctAdd(), BigDecimal.ZERO);
                    List<DeptStockDto> deptArtStockLs = deptStockLs.stream().filter(p -> p.getArtId().equals(deptArt.getArtId()) && Convert.toInt(p.getPackCells(), 0) > 0).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(deptArtStockLs)) {
                        BigDecimal totalPacks = BigDecimal.ZERO;
                        BigDecimal totalAmount = BigDecimal.ZERO;
                        Integer packCells = deptArtStockLs.get(0).getPackCells();

                        for (DeptStockDto stock : deptArtStockLs) {
                            BigDecimal stockTotalPacks = Convert.toBigDecimal(stock.getTotalCells(), BigDecimal.ZERO).divide(Convert.toBigDecimal(stock.getPackCells()), 6, RoundingMode.HALF_UP)
                                    .add(Convert.toBigDecimal(stock.getTotalPacks(), BigDecimal.ZERO));
                            BigDecimal stockAmount = stockTotalPacks.multiply(stock.getPackPrice());
                            totalPacks = totalPacks.add(stockTotalPacks);
                            totalAmount = totalAmount.add(stockAmount);
                        }

                        if (totalPacks.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal avgCostPrice = totalAmount.divide(totalPacks, 4, RoundingMode.HALF_UP);
                            deptArt.setAvgCostPrice(avgCostPrice);
                            deptArt.setAvgCostCellPrice(avgCostPrice.divide(Convert.toBigDecimal(packCells), 6, RoundingMode.HALF_UP));
                            if (orgCustMap != null) {
                                // 这里只算整包平均售价，几个cellPrice都传了BigDecimal.ZERO
                                ArtPriceModel priceModel = orgArtService.findPrice(orgCustMap.getPricingMethod(), packCells,
                                        Convert.toBigDecimal(orgPackPrice, avgCostPrice), BigDecimal.ZERO, Convert.toBigDecimal(lastBuyPrice, Convert.toBigDecimal(orgPackPrice, avgCostPrice)),
                                        avgCostPrice, BigDecimal.ZERO, pctAdd);
                                deptArt.setAvgSalePrice(priceModel.getPackPrice());
                                deptArt.setAvgSaleCellPrice(deptArt.getAvgSalePrice().divide(Convert.toBigDecimal(packCells), 6, RoundingMode.HALF_UP));
                            }
                        }
                    }
                });
            });
        }
    }

    @Override
    public List<DeptArtDto> queryList(long orgId, String deptCode) {
        return queryList(orgId, deptCode, new HashMap<>());
    }

    @Override
    public List<DeptArtDto> queryList(long orgId, String deptCode, Map<String, Object> params) {
        QueryWrapper<DeptArtDto> wrapper = buildQueryWrapper(orgId, deptCode, params);
        List<DeptArtDto> list = this.baseMapper.queryDtoPage(wrapper);
        buildDtoList(list);

        return list;
    }

    /**
     * 根据条件查询
     * 可作为公共方法使用，有传条件才查，不传条件不查
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public List<DeptArtDto> findListByWhere(long orgId, Map<String, Object> params) {
        QueryWrapper<DeptArtDto> wrapper = new GQueryWrapper<DeptArtDto>().getWrapper(params);
        wrapper.eq("t_dept_art.org_id", orgId);
        if (params.containsKey("artId")) {
            wrapper.eq("t_article.Art_ID", Convert.toLong(params.get("artId")));
        }
        //hasStock=1，只查有库存的数据
        Integer hasStock = Convert.toInt(params.get("hasStock"));
        if (hasStock != null && hasStock == 1) {
            wrapper.apply("(ifnull(t_dept_art.Total_Packs, 0) <> 0 or ifnull(t_dept_art.Total_Cells,0) <> 0)");
        }
        List<DeptArtDto> list = this.baseMapper.queryDtoPage(wrapper);
        buildDtoList(list);
        return list;
    }

    @Override
    public DeptArtEntity findById(Long orgId, String deptCode, Long artId) {
        return getOne(new LambdaQueryWrapper<DeptArtEntity>()
                .eq(DeptArtEntity::getOrgId, orgId)
                .eq(DeptArtEntity::getDeptCode, deptCode)
                .eq(DeptArtEntity::getArtId, artId)
        );
    }

    @Override
    public DeptArtDto findDtoById(long orgId, String deptCode, Long artId) {
        DeptArtDto dto = this.baseMapper.findDtoById(orgId, deptCode, artId);
        if (dto == null) {
            dto = new DeptArtDto();
            ArticleEntity articleEntity = articleService.getById(artId);
            dto.setArtCode(articleEntity.getArtCode());
            dto.setArtName(articleEntity.getArtName());
            dto.setArtSpec(articleEntity.getArtSpec());
            dto.setProducer(articleEntity.getProducer());
            dto.setPackMaterial(articleEntity.getPackMaterial());
            dto.setPackUnit(articleEntity.getPackUnit());
            dto.setCellUnit(articleEntity.getCellUnit());
            dto.setDoseUnit(articleEntity.getDoseUnit());
            dto.setPackCells(articleEntity.getPackCells());
            dto.setCellDoses(articleEntity.getCellDoses());
            dto.setArtId(articleEntity.getArtId());
            dto.setOrgId(orgId);
            dto.setDeptCode(deptCode);
        }
        buildDto(dto);
        return dto;
    }

    @Override
    public void updateEntity(long orgId, DeptArtEntity params) {
        ArticleEntity articleEntity = articleService.getById(params.getArtId());
        if (articleEntity.getPackCells() == null || articleEntity.getPackCells().equals(1)) {
            params.setSplittable(0);
        }
        this.update(new LambdaUpdateWrapper<DeptArtEntity>()
                .eq(DeptArtEntity::getOrgId, orgId)
                .eq(DeptArtEntity::getDeptCode, params.getDeptCode())
                .eq(DeptArtEntity::getArtId, params.getArtId())
                .set(DeptArtEntity::getSplittable, params.getSplittable())
                .set(DeptArtEntity::getSafeCells, params.getSafeCells())
                .set(DeptArtEntity::getRackNo, params.getRackNo())
        );
    }

    @Override
    public void purchaseIn(Long orgId, String deptCode, Long artId, BigDecimal totalCells, Integer splittable, DeptArtEntity initDeptArt) {
        DeptArtEntity deptArtEntity = initDeptArt != null ? initDeptArt : findById(orgId, deptCode, artId);
        if (deptArtEntity == null) {
            deptArtEntity = new DeptArtEntity();
            deptArtEntity.setOrgId(orgId);
            deptArtEntity.setDeptCode(deptCode);
            deptArtEntity.setArtId(artId);
            deptArtEntity.setSplittable(splittable);

            this.save(deptArtEntity);
        }
        this.baseMapper.updatePurchaseIn(orgId, deptCode, artId, new Date(), totalCells);
        this.updateDetpArtShortFlag(orgId, deptCode, artId, ArtShortFlag.NORMAL);
    }

    @Override
    public void stockInc(Long orgId, String deptCode, Long artId, Integer totalPacks, BigDecimal totalCells, DeptArtEntity initDeptArt) {
        DeptArtEntity deptArtEntity = initDeptArt != null ? initDeptArt : findById(orgId, deptCode, artId);
        log.debug("stockInc deptArtEntity:{}", deptArtEntity);
        if (deptArtEntity == null) {
            deptArtEntity = new DeptArtEntity();
            deptArtEntity.setOrgId(orgId);
            deptArtEntity.setDeptCode(deptCode);
            deptArtEntity.setArtId(artId);
            deptArtEntity.setTotalPacks(totalPacks);
            deptArtEntity.setTotalCells(totalCells);
            this.save(deptArtEntity);
        } else {
            this.baseMapper.stockInc(orgId, deptCode, artId, totalPacks, totalCells);
        }
    }

//    @Override
//    public ArtStockChangeResult batchStockInc(Long orgId, String deptCode, Long artId, Integer stockNo, Integer totalPacks, BigDecimal totalCells, DeptStockEntity initDeptStock) {
//        log.debug("batchStockInc orgId:{}, deptCode:{}, artId:{}, stockNo:{}, totalPacks:{}, totalCells:{}", orgId, deptCode, artId, stockNo, totalPacks, totalCells);
//        // 增加批号库存
//        return deptStockService.batchStockInc(orgId, deptCode, artId, stockNo, totalPacks, totalCells);
//    }

    @Override
    public void stockDec(Long orgId, String deptCode, Long artId, Integer packCells, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable, DeptArtEntity initDeptArt) {
        log.debug("stockDec orgId:{}, deptCode:{}, artId:{}, packCells:{}, totalPacks:{}, totalCells:{}, checkSplittable:{}", orgId, deptCode, artId, packCells, totalPacks, totalCells, checkSplittable);
        totalPacks = Math.abs(Convert.toInt(totalPacks, 0));
        totalCells = Convert.toBigDecimal(totalCells, BigDecimal.ZERO).abs();
        packCells = Convert.toInt(packCells, 1);
        // 先将拆零数取出整包数量
        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = totalCells.divide(BigDecimal.valueOf(packCells), 0, RoundingMode.DOWN).intValue();
            totalPacks = totalPacks + tempPacks;
            totalCells = totalCells.subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
        }

        DeptArtEntity deptArtEntity = initDeptArt != null ? initDeptArt : findById(orgId, deptCode, artId);
        log.debug("stockDec deptArtEntity:{}", deptArtEntity);
        if (deptArtEntity == null) {
            // 按理说不应该出现，暂时不抛异常，通过日志反推出现原因
            log.error("stockDec 空指针 orgId:{}, deptCode:{}, artId:{}, packCells:{}, totalPacks:{}, totalCells:{}, checkSplittable:{}", orgId, deptCode, artId, packCells, totalPacks, totalCells, checkSplittable);
            deptArtEntity = new DeptArtEntity();
            deptArtEntity.setOrgId(orgId);
            deptArtEntity.setDeptCode(deptCode);
            deptArtEntity.setArtId(artId);
            this.save(deptArtEntity);
        }
        if (checkSplittable && totalCells.compareTo(BigDecimal.ZERO) > 0 && Convert.toInt(deptArtEntity.getSplittable(), 0) == 0) { // 不允许拆零, 向上取整包数量
            totalCells = BigDecimal.ZERO;
            totalPacks = totalPacks + 1;
        }

        if (deptArtEntity.getTotalPacks() == null) {
            deptArtEntity.setTotalPacks(0);
        }
        if (deptArtEntity.getTotalCells() == null) {
            deptArtEntity.setTotalCells(BigDecimal.ZERO);
        }

        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(deptArtEntity.getTotalCells()) > 0) { // 需求要拆零，拆零数量缺少
            totalPacks = totalPacks + 1; // 整包数量+1
            BigDecimal tempCells = BigDecimal.valueOf(packCells).subtract(totalCells); // 剩余数量
            totalCells = tempCells.negate();
            deptArtEntity.setTotalCells(deptArtEntity.getTotalCells().subtract(totalCells));
        }

        int needPacks = totalPacks - deptArtEntity.getTotalPacks();
        // 整包数不够，从拆零数量中锁定
        if (totalPacks > 0 && needPacks > 0 && deptArtEntity.getTotalCells().compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = deptArtEntity.getTotalCells().divide(BigDecimal.valueOf(packCells), 0, RoundingMode.DOWN).intValue();
            if (needPacks <= tempPacks) {
                totalPacks = totalPacks - needPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(needPacks)));
            } else {
                totalPacks = totalPacks - tempPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
            }
        }

        this.baseMapper.stockDec(orgId, deptCode, artId, totalPacks, totalCells);
    }

    /**
     * 减少t_dept_art库存，返回更新条数
     * @param orgId
     * @param deptCode
     * @param articleEntity
     * @param totalPacks
     * @param totalCells
     * @param checkSplittable
     * @return
     */
    @Override
    public int stockDecCount(Long orgId, String deptCode, ArticleEntity articleEntity, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable) {
        log.debug("stockDecCount orgId:{}, deptCode:{}, articleEntity:{}, totalPacks:{}, totalCells:{}, checkSplittable:{}", orgId, deptCode, articleEntity, totalPacks, totalCells, checkSplittable);
        if (totalPacks != null) {
            totalPacks = Math.abs(totalPacks);
        } else {
            totalPacks = 0;
        }
        if (totalCells != null) {
            totalCells = totalCells.abs();
        } else {
            totalCells = BigDecimal.ZERO;
        }
        // 获取商品拆零系数
        Integer packCells = 1;
        if (articleEntity.getPackCells() != null) {
            packCells = articleEntity.getPackCells();
        }
        // 先将拆零数取出整包数量
        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = totalCells.divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            totalPacks = totalPacks + tempPacks;
            totalCells = totalCells.subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
        }

        DeptArtEntity deptArtEntity = findById(orgId, deptCode, articleEntity.getArtId());
        log.debug("stockDecCount deptArtEntity:{}", deptArtEntity);
        if (deptArtEntity == null) {
            deptArtEntity = new DeptArtEntity();
            deptArtEntity.setOrgId(orgId);
            deptArtEntity.setDeptCode(deptCode);
            deptArtEntity.setArtId(articleEntity.getArtId());
            this.save(deptArtEntity);
        }
        if (checkSplittable && totalCells.compareTo(BigDecimal.ZERO) > 0 && Convert.toInt(deptArtEntity.getSplittable(), 0) == 0) { // 不允许拆零, 向上取整包数量
            totalCells = BigDecimal.ZERO;
            totalPacks = totalPacks + 1;
        }

        if (deptArtEntity.getTotalPacks() == null) {
            deptArtEntity.setTotalPacks(0);
        }
        if (deptArtEntity.getTotalCells() == null) {
            deptArtEntity.setTotalCells(BigDecimal.ZERO);
        }

        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(deptArtEntity.getTotalCells()) > 0) { // 需求要拆零，拆零数量缺少
            totalPacks = totalPacks + 1; // 整包数量+1
            BigDecimal tempCells = BigDecimal.valueOf(packCells).subtract(totalCells); // 剩余数量
            totalCells = tempCells.negate();
            deptArtEntity.setTotalCells(deptArtEntity.getTotalCells().subtract(totalCells));
        }

        int needPacks = totalPacks - deptArtEntity.getTotalPacks();
        // 整包数不够，从拆零数量中锁定
        if (totalPacks > 0 && needPacks > 0 && deptArtEntity.getTotalCells().compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = deptArtEntity.getTotalCells().divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            if (needPacks <= tempPacks) {
                totalPacks = totalPacks - needPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(needPacks)));
            } else {
                totalPacks = totalPacks - tempPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
            }
        }

        int count = this.baseMapper.stockDecCount(orgId, deptCode, articleEntity.getArtId(), totalPacks, totalCells);
        return count;
    }


    @Override
    public ArtStockReserveResult batchStockDec(Long orgId, String deptCode, ArticleEntity articleEntity, String batchNo, Integer stockNo, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable) {
        log.debug("batchStockDec orId:{}, deptCode:{}, articleEntity:{}, batchNo:{}, totalPacks:{}, totalCells:{}",
                orgId, deptCode, articleEntity, batchNo, totalPacks, totalCells);
        ArtStockReserveResult result = new ArtStockReserveResult();
        result.setArtId(articleEntity.getArtId());
        result.setTotalPacks(totalPacks);
        result.setTotalCells(totalCells);
        result.setReservedFlag(ReservedFlag.NOT_STOCK);

        List<DeptStockDto> deptStockDtoList = new ArrayList<>();
        // 初始化默认值
        Integer packCells = 1;
        if (articleEntity.getPackCells() != null) {
            packCells = articleEntity.getPackCells();
        }
        if (totalPacks == null) {
            totalPacks = 0;
        }
        if (totalCells == null) {
            totalCells = BigDecimal.ZERO;
        }
        // 先将拆零数取出整包数量
        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = totalCells.divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            totalPacks = totalPacks + tempPacks;
            totalCells = totalCells.subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
        }
        DeptArtEntity deptArtEntity = findById(orgId, deptCode, articleEntity.getArtId());
        if (deptArtEntity == null) {
            log.error("未找到仓库商品记录，deptCode:" + deptCode + ", artId=" + articleEntity.getArtId() + ", artName=" + articleEntity.getArtName());
//            throw new SaveFailureException("未找到仓库商品记录，deptCode:" + deptCode + ", artId=" + articleEntity.getArtId() + ", artName=" + articleEntity.getArtName());
            result.setDeptStockDtos(deptStockDtoList);
            return result;
        }
        if (deptArtEntity.getCountId() != null) {
            throw new SaveFailureException(articleEntity.getArtId() + ":" + articleEntity.getArtName() + "该品种正在盘点中，不能进行操作。盘点单：" + deptArtEntity.getCountId());
        }
        if (checkSplittable && totalCells.compareTo(BigDecimal.ZERO) > 0 && Convert.toInt(deptArtEntity.getSplittable(), 0) == 0) { // 不允许拆零
            totalCells = BigDecimal.ZERO;
            totalPacks = totalPacks + 1;
        }
        // 获取有库存的列表
        List<DeptStockDto> deptStockEntities = deptStockService.findListByArtId(orgId, deptCode, articleEntity.getArtId());
        if (stockNo != null) { // 按批次号筛选
            deptStockEntities = deptStockEntities.stream().filter(p -> p.getStockNo().equals(stockNo)).collect(Collectors.toList());
        } else if (StrUtil.isNotBlank(batchNo)) { // 按批号筛选
            deptStockEntities = deptStockEntities.stream().filter(p -> p.getBatchNo().equals(batchNo)).collect(Collectors.toList());
        }
        // 过滤有库存的数据
        deptStockEntities = deptStockEntities.stream().filter(p -> (p.getTotalPacks() != null && p.getTotalPacks() > 0)
                || (p.getTotalCells() != null && p.getTotalCells().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());

        log.debug("找到{}条满足条件的记录:{}", deptStockEntities.size(), JSONUtil.toJsonStr(deptStockEntities));
        Integer packsReserved = 0; // 已锁定整包数量
        BigDecimal cellsReserved = BigDecimal.ZERO; // 已锁定拆零数量

        for (DeptStockDto deptStockEntity : deptStockEntities) {
            log.debug("正在处理,totalPacks: {}, totalCells: {}, deptStockEntity：{}", totalPacks, totalCells, JSONUtil.toJsonStr(deptStockEntity));
            ArtStocknoEntity artStocknoEntity = artStocknoService.findById(deptStockEntity.getArtId(), deptStockEntity.getStockNo());
            if (artStocknoEntity.getPackCells() == null) {
                artStocknoEntity.setPackCells(1);
            }
//            if (!artStocknoEntity.getPackCells().equals(packCells)) {
//                throw new SaveFailureException("当前批次的包装制剂数与三目中维护的不一致，请联系管理员进行处理。artId:"
//                        + artStocknoEntity.getArtId() + ", artName:" + articleEntity.getArtName()
//                        + ", batchNo:" + deptStockEntity.getBatchNo() + ", stockNo:" + artStocknoEntity.getStockNo()
//                + ", 批次中包装制剂数：" + artStocknoEntity.getPackCells() + ", 三目中包装制剂数：" + packCells);
//            }
            if (deptStockEntity.getTotalPacks() == null) {
                deptStockEntity.setTotalPacks(0);
            }
            if (deptStockEntity.getTotalCells() == null) {
                deptStockEntity.setTotalCells(BigDecimal.ZERO);
            }
            boolean lockFlag = false;
            Integer lockTotalPacks = 0; // 锁定的整包数量
            BigDecimal lockTotalCells = BigDecimal.ZERO; // 锁定的拆零数量

            Integer billTotalPacks = 0; // 满足的整包数量
            BigDecimal billTotalCells = BigDecimal.ZERO; // 满足的拆零数量
            if (totalPacks > 0 && deptStockEntity.getTotalPacks() > 0) { // 需求要整包，并且库存有整包
                if (totalPacks <= deptStockEntity.getTotalPacks()) { // 库存全部满足
                    deptStockEntity.setTotalPacks(deptStockEntity.getTotalPacks() - totalPacks);
                    lockTotalPacks = totalPacks;
                    billTotalPacks = totalPacks;
                    totalPacks = 0;
                } else {
                    // 整包数量缺少，稍后继续查找下一个批号
                    lockTotalPacks = deptStockEntity.getTotalPacks();
                    billTotalPacks = deptStockEntity.getTotalPacks();
                    totalPacks = totalPacks - deptStockEntity.getTotalPacks();
                    deptStockEntity.setTotalPacks(0);
                }
                lockFlag = true;
                log.debug("需求要整包，并且库存有整包,lockTotalPacks: {}, billTotalPacks: {}, totalPacks：{}", lockTotalPacks, billTotalPacks, totalPacks);
            }
            // 整包数不够，从拆零数量中锁定
            if (totalPacks > 0 && deptStockEntity.getTotalCells().compareTo(BigDecimal.valueOf(packCells)) >= 0) {
                int tempPacks = deptStockEntity.getTotalCells().divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
                int tempLockPacks;
                if (totalPacks <= tempPacks) {
                    tempLockPacks = totalPacks;
                } else {
                    tempLockPacks = tempPacks;
                }
                billTotalPacks = billTotalPacks + tempLockPacks;
                lockTotalCells = BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempLockPacks));
                totalPacks = totalPacks - tempLockPacks;
                deptStockEntity.setTotalCells(deptStockEntity.getTotalCells().subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempLockPacks))));
                lockFlag = true;
                log.debug("整包数不够，从拆零数量中锁定,lockTotalPacks: {}, billTotalPacks: {}, totalPacks：{}, lockTotalCells: {}", lockTotalPacks, billTotalPacks, totalPacks, lockTotalCells);
            }
            if (totalCells.compareTo(BigDecimal.ZERO) > 0) { // 需求要拆零数量
                if (totalCells.compareTo(deptStockEntity.getTotalCells()) <= 0) { // 库存全部满足
                    deptStockEntity.setTotalCells(deptStockEntity.getTotalCells().subtract(totalCells));
                    lockTotalCells = lockTotalCells.add(totalCells);
                    billTotalCells = billTotalCells.add(totalCells);
                    totalCells = BigDecimal.ZERO;
                } else { // 拆零数量缺少
                    if (deptStockEntity.getTotalPacks() > 0) { // 还有整包, 新拆一包来满足
                        deptStockEntity.setTotalPacks(deptStockEntity.getTotalPacks() - 1); // 整包库存-1
                        lockTotalPacks = lockTotalPacks + 1; // 锁定整包数量+1

                        BigDecimal tempCells = BigDecimal.valueOf(packCells).subtract(totalCells); // 剩余数量
                        lockTotalCells = lockTotalCells.add(tempCells.negate());
                        billTotalCells = billTotalCells.add(totalCells);
                        totalCells = BigDecimal.ZERO;
                        deptStockEntity.setTotalCells(deptStockEntity.getTotalCells().add(tempCells)); // 将拆零后未锁定的数量加到拆零库存中
                    } else { // 没有整包了，只能满足部分拆零数量
                        totalCells = totalCells.subtract(deptStockEntity.getTotalCells());
                        lockTotalCells = lockTotalCells.add(deptStockEntity.getTotalCells());
                        billTotalCells = billTotalCells.add(deptStockEntity.getTotalCells());
                        deptStockEntity.setTotalCells(BigDecimal.ZERO);
                    }
                }
                lockFlag = true;
                log.debug("需求要拆零数量,totalCells: {}, lockTotalCells: {}, billTotalCells：{}", totalCells, lockTotalCells, billTotalCells);
            }
            // 允许拆零时，如果还有拆零数量，一起将拆零数量出完
            if (totalPacks > 0 && deptStockEntity.getTotalCells().compareTo(BigDecimal.ZERO) > 0 && Convert.toInt(deptArtEntity.getSplittable(), 0) == 1) {
                totalPacks = totalPacks - 1;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).subtract(deptStockEntity.getTotalCells()));
                lockTotalCells = lockTotalCells.add(deptStockEntity.getTotalCells());
                billTotalCells = billTotalCells.add(deptStockEntity.getTotalCells());
                deptStockEntity.setTotalCells(BigDecimal.ZERO);
                lockFlag = true;
                log.debug("允许拆零时，如果还有拆零数量，一起将拆零数量出完,totalPacks: {}, totalCells: {}, lockTotalCells：{}, billTotalCells: {}", totalPacks, totalCells, lockTotalCells, billTotalCells);
            }
            if (lockFlag) {
                // 减少批号库存
                ArtStockChangeResult artStockChangeResult = deptStockService.batchStockDec(orgId, deptCode, articleEntity.getArtId(), deptStockEntity.getStockNo(), lockTotalPacks, lockTotalCells);

                DeptStockDto dto = new DeptStockDto();
                dto.setArtId(deptStockEntity.getArtId());
                dto.setOrgId(deptStockEntity.getOrgId());
                dto.setDeptCode(deptStockEntity.getDeptCode());
                dto.setStockNo(deptStockEntity.getStockNo());
                dto.setBatchNo(deptStockEntity.getBatchNo());
                dto.setExpiry(deptStockEntity.getExpiry());
                dto.setDateManufactured(deptStockEntity.getDateManufactured());
                dto.setTotalPacks(billTotalPacks);
                dto.setTotalCells(billTotalCells);

                dto.setLastTotalPacks(artStockChangeResult.getLastTotalPacks());
                dto.setLastTotalCells(artStockChangeResult.getLastTotalCells());
                deptStockDtoList.add(dto);

//                packsReserved = packsReserved + lockTotalPacks;
//                cellsReserved = cellsReserved.add(lockTotalCells);
                packsReserved = packsReserved + billTotalPacks;
                cellsReserved = cellsReserved.add(billTotalCells);
            }
            if (totalPacks == 0 && totalCells.compareTo(BigDecimal.ZERO) == 0) {
                log.debug("需求数量全部满足,退出循环");
                break;
            }
        }
//        if (totalPacks > 0 || totalCells.compareTo(BigDecimal.ZERO) > 0) {
//            throw new SaveFailureException(articleEntity.getArtName() + "库存不满足");
//        }
        if (packsReserved.equals(0) && cellsReserved.compareTo(BigDecimal.ZERO) == 0) {
            result.setReservedFlag(ReservedFlag.NOT_STOCK);
            // 库存短缺
            updateDetpArtShortFlag(orgId, deptCode, articleEntity.getArtId(), ArtShortFlag.SHORTAGE);
        } else if (totalPacks > 0 || totalCells.compareTo(BigDecimal.ZERO) > 0) {
            result.setReservedFlag(ReservedFlag.PARTIAL_LOCKED);
            // 库存紧张
            updateDetpArtShortFlag(orgId, deptCode, articleEntity.getArtId(), ArtShortFlag.TIGHT);
        } else {
            result.setReservedFlag(ReservedFlag.All_LOCKED);
        }
        result.setPacksReserved(packsReserved);
        result.setCellsReserved(cellsReserved);
        result.setDeptStockDtos(deptStockDtoList);
        log.debug("batchStockDec finished orId:{}, deptCode:{}, articleEntity:{}, batchNo:{}, totalPacks:{}, totalCells:{}, result:{}",
                orgId, deptCode, articleEntity, batchNo, totalPacks, totalCells, result);

        this.addReservedCells(orgId, deptCode, articleEntity.getArtId(), ArticleUtil.packsToCells(packsReserved, packCells, cellsReserved));
        return result;
    }

    @Override
    public List<DeptArtDto> calcReqList(long orgId, String deptCode, Integer type, Integer days) {
        // 获取当前仓库所有商品
        List<DeptArtDto> list = this.queryList(orgId, deptCode);
        if (type == 1) { // 保安全库存
            // 已经没有库存或者库存不足安全库存
            list = list.stream().filter(p -> p.getCurrentCells().compareTo(BigDecimal.ZERO) == 0 ||
                    p.getCurrentCells().compareTo(p.getSafeCells() == null ? BigDecimal.ZERO : BigDecimal.valueOf(p.getSafeCells())) < 0).collect(Collectors.toList());
            // 给每个商品计算出应该补货的量
            list.forEach(p -> {
                // 计算补货量
                BigDecimal safeCells = p.getSafeCells() == null ? BigDecimal.ZERO : BigDecimal.valueOf(p.getSafeCells());
                BigDecimal reqCells = safeCells.subtract(p.getCurrentCells());
                // 计算整包数量, 整包数量=补货量/包装制剂数向下取整
                p.setReqTotalPacks(reqCells.divide(BigDecimal.valueOf(p.getPackCells()), 0, BigDecimal.ROUND_DOWN).intValue());
                // 计算拆零数量， 拆零数量=补货量-(整包数量*包装制剂数)
                p.setReqTotalCells(reqCells.subtract(BigDecimal.valueOf(p.getReqTotalPacks()).multiply(BigDecimal.valueOf(p.getPackCells()))));
            });
        } else if (type == 2) { // 保可用天数
            // 可用天数不为空并且小于days
            list = list.stream().filter(p -> p.getCanUseDays() != null && p.getCanUseDays() < days).collect(Collectors.toList());
            list.forEach(p -> {
                // 计算补货量，补货量=可用天数*日销量
                BigDecimal reqCells = BigDecimal.valueOf(days - p.getCanUseDays()).multiply(p.getUsedByDay());
                p.setReqTotalPacks(reqCells.divide(BigDecimal.valueOf(p.getPackCells()), 0, BigDecimal.ROUND_DOWN).intValue());
                p.setReqTotalCells(reqCells.subtract(BigDecimal.valueOf(p.getReqTotalPacks()).multiply(BigDecimal.valueOf(p.getPackCells()))));
            });
        } else if (type == 3) { // 全部库存
            list.forEach(p -> {
                p.setReqTotalPacks(p.getTotalPacks());
                p.setReqTotalCells(p.getTotalCells());
            });
        }
        list = list.stream().filter(p -> (p.getReqTotalPacks() != null && p.getReqTotalPacks() > 0)
                || (p.getReqTotalCells() != null && p.getReqTotalCells().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<DeptStockDto> calcReqListByWbSeqid(long orgId, String deptCode, Long wbSeqid) {
        List<DeptStockDto> list = baseMapper.calcReqListByWbSeqid(orgId, deptCode, wbSeqid);
        return list;
    }

    @Override
    public List<ArtBatchEntity> getArtBatchNoList(long orgId, String deptCode, Long artId) {
        return baseMapper.getArtBatchNoList(orgId, deptCode, artId);
    }

    /**
     * 查盘点单所有的批次商品信息
     * @param orgId
     * @param deptCode
     * @param countId
     * @return
     */
    @Override
    public List<WmCountDetailEntity> getArtBatchNoListByCountId(long orgId, String deptCode, Long countId) {
        List<WmCountDetailEntity> list = baseMapper.getArtBatchNoListByCountId(orgId, deptCode, countId);
        list.forEach(item -> {
            item.setCountId(countId);
        });
        return list;
    }

    @Override
    public void addReservedCells(Long orgId, String deptCode, Long artId, BigDecimal reservedCells) {
        log.info("reservedCells add orgId:{} deptCode:{} artId:{} reservedCells:{}", orgId, deptCode, artId, reservedCells);
        this.baseMapper.updateReservedCells(orgId, deptCode, artId, reservedCells);
    }

    @Override
    public void subReservedCells(Long orgId, String deptCode, Long artId, BigDecimal reservedCells) {
        log.info("reservedCells sub orgId:{} deptCode:{} artId:{} reservedCells:{}", orgId, deptCode, artId, reservedCells);
        this.baseMapper.updateReservedCells(orgId, deptCode, artId, reservedCells.negate());
    }

    @Override
    public List<DeptArtDto> queryAllOrgDeptArtList(Map<String, Object> params) {
        QueryWrapper<DeptArtDto> wrapper = new GQueryWrapper<DeptArtDto>().getWrapper(params);
        Integer orgId = Convert.toInt(params.get("orgId"));
        if (orgId != null) {
            wrapper.eq("t_dept_art.org_id", orgId);
        }
        String deptCode = Convert.toStr(params.get("deptCode"));
        if (ObjectUtil.isNotEmpty(deptCode)) {
            wrapper.eq("t_dept_art.dept_code", deptCode);
        }
        if (params.containsKey("artIds")) {
            wrapper.in("t_article.Art_ID", Convert.toLongArray(params.get("artIds")));
        }
        List<Long> artIdLs = Convert.toList(Long.class, params.get("artIdLs"));
        if (ObjectUtil.isNotEmpty(artIdLs)) {
            wrapper.in("t_article.Art_ID", artIdLs);
        } else {
            throw new SaveFailureException("artIdLs 不能为空");
        }
        Integer hasStock = Convert.toInt(params.get("hasStock"), 0);
        if (hasStock == 1) {
            wrapper.apply("(ifnull(t_dept_art.Total_Packs, 0) <> 0 or ifnull(t_dept_art.Total_Cells,0) <> 0)");
        }
        return baseMapper.queryAllOrgDeptArtList(wrapper);
    }

    @Async
    protected void updateDetpArtShortFlag(long orgId, String deptCode, Long artId, ArtShortFlag artShortFlag) {
        this.update(new LambdaUpdateWrapper<DeptArtEntity>()
                .eq(DeptArtEntity::getOrgId, orgId)
                .eq(DeptArtEntity::getDeptCode, deptCode)
                .eq(DeptArtEntity::getArtId, artId)
                .set(DeptArtEntity::getShortFlag, artShortFlag.getValue())
        );
    }
    public void remakeDeptArt(Long orgId, String deptCode, Long artId){
        this.baseMapper.remakeDeptArt( orgId,  deptCode,  artId);
    }

}
