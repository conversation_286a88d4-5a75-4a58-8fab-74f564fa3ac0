package cn.feiying.med.clinics_wm.dao;

import cn.feiying.med.clinics_wm.dto.DeptArtDto;
import cn.feiying.med.clinics_wm.dto.DeptStockDto;
import cn.feiying.med.clinics_wm.entity.ArtBatchEntity;
import cn.feiying.med.clinics_wm.entity.DeptArtEntity;
import cn.feiying.med.clinics_wm.entity.WmCountDetailEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 仓库商品库存表
 *
 * <AUTHOR> 2024-06-04 18:48:02
 */
@Mapper
public interface DeptArtDao extends BaseMapper<DeptArtEntity> {

    void stockInc(Long orgId, String deptCode, Long artId, Integer totalPacks, BigDecimal totalCells);

    void stockDec(Long orgId, String deptCode, Long artId, int totalPacks, BigDecimal totalCells);

    /**
     * 减少t_dept_art库存，返回更新条数
     * @param orgId
     * @param deptCode
     * @param artId
     * @param totalPacks
     * @param totalCells
     * @return
     */
    int stockDecCount(Long orgId, String deptCode, Long artId, int totalPacks, BigDecimal totalCells);

    IPage<DeptArtDto> queryDtoPage(IPage<DeptArtDto> page, @Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);
    List<DeptArtDto> queryDtoPage(@Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);

    void updatePurchaseIn(Long orgId, String deptCode, Long artId, Date lastSupplemented, BigDecimal totalCells);

    DeptArtDto findDtoById(long orgId, String deptCode, Long artId);

    List<ArtBatchEntity> getArtBatchNoList(long orgId, String deptCode, Long artId);

    /**
     * 查盘点单所有的批次商品信息
     * @param orgId
     * @param deptCode
     * @param countId
     * @return
     */
    List<WmCountDetailEntity> getArtBatchNoListByCountId(long orgId, String deptCode, Long countId);


    void updateReservedCells(Long orgId, String deptCode, Long artId, BigDecimal reservedCells);

    List<DeptArtDto> queryAllOrgDeptArtList(@Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);

    List<DeptStockDto> calcReqListByWbSeqid(long orgId, String deptCode, Long wbSeqid);

    void remakeDeptArt(Long orgId, String deptCode, Long artId);

}
