package cn.feiying.med.mcisp.misdk.vSDDW_DLL._3101;

import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.mpi.entity.PatientEntity;
import cn.feiying.med.hip.mpi.service.PatientService;
import cn.feiying.med.mcisp.entity.MiCaseEntity;
import cn.feiying.med.mcisp.entity.MiSelfpaidPctEntity;
import cn.feiying.med.mcisp.entity.TrigSceneEntity;
import cn.feiying.med.mcisp.enums.TrigScene;
import cn.feiying.med.mcisp.misdk.common.BaseAction;
import cn.feiying.med.mcisp.misdk.vSDDW_DLL._3101.req.*;
import cn.feiying.med.mcisp.model.ApiParams;
import cn.feiying.med.mcisp.service.MiCaseService;
import cn.feiying.med.mcisp.service.MiSelfpaidPctService;
import cn.feiying.med.mcisp.service.TrigSceneService;
import cn.feiying.med.microhis.bcs.entity.BillDetailEntity;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.entity.CashEntity;
import cn.feiying.med.microhis.bcs.service.BillDetailService;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.bcs.service.CashService;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;

import cn.hutool.db.sql.Order;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("action3101_vSDDW_DLL")
@RequiredArgsConstructor
public class Action3101 extends BaseAction {

    private final PatientService patientService;
    private final MiCaseService miCaseService;
    private final VisitDeptService visitDeptService;
    private final VisitBedService visitBedService;
    private final BedService bedService;
    private final RoomService roomService;
    private final VisitExtraService visitExtraService;
    private final InpatientService inpatientService;
    private final VisitDiagService visitDiagService;
    private final AdmDiagService admDiagService;
    private final DisDiagService disDiagService;
    private final BillService billService;
    private final BillDetailService billDetailService;
    private final RecipeService recipeService;
    private final RecipeExtraService recipeExtraService;
    private final OrderService orderService;
    private final OrderEntryService orderEntryService;
    private final ArticleService articleService;
    private final MiSelfpaidPctService miSelfpaidPctService;
    private final OrgDeptService orgDeptService;
    private final ClinicianService clinicianService;
    private final OeExtraService oeExtraService;
    private final OrgItemPriceService orgItemPriceService;
    private final ClinicsService clinicsService;
    private final ClinicsCatService clinicsCatService;
    private final FeeTypeService feeTypeService;
    private final TrigSceneService trigSceneService;
    private final CashService cashService;

    @Override
    public Object before(ApiParams params) {
        // 业务逻辑待实现
//        Assert.isFalse(params.getOe_list().isEmpty(), "医嘱号不能为空");
        if(params.getCash_id() != null){
            CashEntity cash = cashService.getById(params.getCash_id());
            Assert.notNull(cash, "未找到结算信息");
            params.setVisit_id(cash.getVisitId());
        }
        Assert.notNull(params.getVisit_id(), "诊疗号不能为空");
        Long visitId = params.getVisit_id();
        VisitEntity visit = visitService.getById(visitId);
        PatientEntity patient = patientService.getById(visit.getPatientId());
        Assert.notNull(patient, "未找到患者信息");
        Assert.notNull(patient.getGenderId(), "患者性别不能为空");
        MiCaseEntity miCase = miCaseService.getById(visitId);
        Assert.notNull(miCase, "没有找到医保登记信息");
        Assert.notBlank(miCase.getMdtrtId(), "医保就诊id不能为空，请先进行医保登记");


        PatientDto patientDto = PatientDto.builder()
                .patnId(Convert.toStr(visit.getPatientId()))
                .patnName(visit.getPatientName())
                .gend(Convert.toStr(patient.getGenderId()))
                .brdy(DateUtil.format(DateUtil.parse(patient.getBirthDate().toString(), "yyyyMMdd"), "yyyy-MM-dd"))
                .poolarea(miCase.getPsnAdmdvs() != null ?
                        (miCase.getPsnAdmdvs().length() > 6 ? miCase.getPsnAdmdvs().substring(0, 6) : miCase.getPsnAdmdvs())
                        : null)
                .currMdtrtId(miCase.getMdtrtId())
                .build();
        List<VisitEntity> visitList = new ArrayList<>();
        visitList.add(visit);
        Map<Long, ArticleEntity> artMap = new HashMap<>();
        Map<String, OrgDeptEntity> deptMap = new HashMap<>();
        Map<Long, ClinicianEntity> clinicianMap = new HashMap<>();
        List<EncounterDto> encounterDtos = new ArrayList<>();
        ClinicsEntity clinics = clinicsService.getById(visit.getOrgId());
        ClinicsCatEntity clinicsCat = clinicsCatService.getById(clinics.getCatId());
        for(VisitEntity visitItem: visitList){
            // 获取机构信息
            OrgEntity orgItem = orgService.getById(visitItem.getOrgId());
            // 获取就诊扩展信息
            InpatientEntity inpatientItem = inpatientService.getById(visitItem.getVisitId());
            // 获取医保就诊信息
            MiCaseEntity miCaseItem = miCaseService.getById(visitItem.getVisitId());
            // 获取出院病区信息
            List<VisitDeptEntity> visitDeptList = visitDeptService.list(
                    new LambdaQueryWrapper<VisitDeptEntity>()
                            .eq(VisitDeptEntity::getVisitId, visitItem.getVisitId())
                            .orderByDesc(VisitDeptEntity::getDeptNo)
            );
            VisitDeptEntity lastVisitDept = visitDeptList.get(0);
            // 获取病床列表
            List<VisitBedEntity> visitBedList = visitBedService.list(
                    new LambdaQueryWrapper<VisitBedEntity>()
                            .eq(VisitBedEntity::getVisitId, visitItem.getVisitId())
                            .orderByDesc(VisitBedEntity::getSeqNo)
            );
            BedEntity bed = null;
            RoomEntity room = null;
            if(visitBedList.size()>0){
                VisitBedEntity lastVisitBed = visitBedList.get(0);
                // 获取病房号
                bed = bedService.getById(lastVisitBed.getBedId());
                Assert.notNull(bed,"未找到病床数据");
                // 获取病床号
                room = roomService.getById(bed.getRoomId());
                Assert.notNull(room,"未找到病房数据");
            }
            // 获取诊断信息
            List<VisitDiagEntity> visitDiagList = visitDiagService.list(
                    new LambdaQueryWrapper<VisitDiagEntity>()
                            .eq(VisitDiagEntity::getVisitId, visitItem.getVisitId())
                            .ne(VisitDiagEntity::getDiagStatus, DiagStatus.removed.getValue())
                            .orderByAsc(VisitDiagEntity::getDisplayOrder)
            );
            // 获取入院诊断
            List<AdmDiagEntity> admDiagList = admDiagService.list(
                    new LambdaQueryWrapper<AdmDiagEntity>()
                            .eq(AdmDiagEntity::getVisitId, visitItem.getVisitId())
                            .orderByAsc(AdmDiagEntity::getDisplayOrder)
            );
            // 获取出院诊断
            List<DisDiagEntity> disDiagList = disDiagService.list(
                    new LambdaQueryWrapper<DisDiagEntity>()
                            .eq(DisDiagEntity::getVisitId, visitItem.getVisitId())
                            .orderByAsc(DisDiagEntity::getDisplayOrder)
            );
            VisitDiagEntity mainVisitDiag = null;
            if(!visitDiagList.isEmpty()){
                 mainVisitDiag = visitDiagList.get(0);
            }
            if(!admDiagList.isEmpty()){
                mainVisitDiag = visitDiagList.stream()
                        .filter(visitDiag -> visitDiag.getDiagNo().equals(admDiagList.get(0).getDiagNo()))
                        .findFirst()
                        .orElse(null);
            }
            if(!disDiagList.isEmpty()){
                mainVisitDiag = visitDiagList.stream()
                        .filter(visitDiag -> visitDiag.getDiagNo().equals(disDiagList.get(0).getDiagNo()))
                        .findFirst()
                        .orElse(null);
            }


            ClinicianEntity fistClinician = clinicianService.getById(visitItem.getClinicianId());
            List<VisitDeptEntity> visitDepts = visitDeptService.list(
                    new LambdaQueryWrapper<VisitDeptEntity>()
                            .eq(VisitDeptEntity::getVisitId, visitItem.getVisitId())
                            .orderByAsc(VisitDeptEntity::getDeptNo)
            );
            VisitDeptEntity admDept = visitDepts.get(0);
            VisitDeptEntity dscgDept = visitDepts.get(visitDepts.size()-1);
            EncounterDto encounterDto = EncounterDto.builder()
                    .mdtrtId(miCaseItem.getMdtrtId())
                    .medinsType(clinicsCat.getCatCode())
                    .medinsId(orgItem.getMiOrgCode())
                    .medinsName(orgItem.getOrgName())
                    .medinsAdmdvs(orgItem.getMiTrtZonecode())
                    .medinsLv(Convert.toStr(orgItem.getLevelId()))
                    .wardareaCodg(lastVisitDept.getDeptCode())
                    .wardno(room==null?null:room.getRoomNo())
                    .bedno(bed==null?null:bed.getBedNo())
                    .admDate(DateUtil.format(visitItem.getTimeAdmission(), "yyyy-MM-dd HH:mm:ss"))
                    .dscgDate(DateUtil.format(inpatientItem.getTimeDischarged(), "yyyy-MM-dd HH:mm:ss"))
                    .dscgMainDiseCodg(mainVisitDiag==null?visitDiagList.get(0).getDiagCode():mainVisitDiag.getDiagCode())
                    .dscgMainDiseName(mainVisitDiag==null?visitDiagList.get(0).getDiagName():mainVisitDiag.getDiagName())
                    .drCodg(fistClinician.getClinicianNo())
                    .admDeptCodg(admDept.getDeptCode())
                    .admDeptName(admDept.getDeptName())
                    .dscgDeptCodg(dscgDept.getDeptCode())
                    .dscgDeptName(dscgDept.getDeptName())
                    .medMdtrtType(Convert.toStr(visit.getClinicTypeId()))
                    .medType(Convert.toStr(visit.getMedTypeId()))
                    .matnStas("0")
                    .insutype(Convert.toStr(visitItem.getInsuranceTypeId()))
                    .reimFlag("1")
                    .outSetlFlag("0")
                    .build();

            List<DiagnoseDto> diagnoseDtos = new ArrayList<>();
            for(DisDiagEntity disDiag : disDiagList){
                VisitDiagEntity visitDiag = visitDiagList.stream()
                        .filter(item -> disDiag.getDiagNo().equals(item.getDiagNo()))
                        .findFirst()
                        .orElse(null);
                DiagnoseDto diagnoseDto = DiagnoseDto.builder()
                        .diseId(visitItem.getVisitId()+"-do-"+disDiag.getDiagNo())
                        .inoutDiseType("2")
                        .diasSrtNo(Convert.toStr(disDiag.getDisplayOrder()))
                        .maindiseFlag(visitDiag.getDiagNo().equals(mainVisitDiag.getDiagNo())?"1":"0")
                        .diseCodg(visitDiag.getDiagCode())
                        .diseName(visitDiag.getDiagName())
                        .build();
                if(visitDiag.getTimeDiagnosed()!=null){
                    diagnoseDto.setDiseDate(DateUtil.format(visitDiag.getTimeDiagnosed(),"yyyy-MM-dd HH:mm:ss"));
                }
                diagnoseDtos.add(diagnoseDto);
            }
            for(AdmDiagEntity admDiag : admDiagList){
                VisitDiagEntity visitDiag = visitDiagList.stream()
                        .filter(item -> admDiag.getDiagNo().equals(item.getDiagNo()))
                        .findFirst()
                        .orElse(null);
                DiagnoseDto diagnoseDto = DiagnoseDto.builder()
                        .diseId(visitItem.getVisitId()+"-di-"+admDiag.getDiagNo())
                        .inoutDiseType("1")
                        .diasSrtNo(Convert.toStr(admDiag.getDisplayOrder()))
                        .maindiseFlag(visitDiag.getDiagNo().equals(mainVisitDiag.getDiagNo())?"1":"0")
                        .diseCodg(visitDiag.getDiagCode())
                        .diseName(visitDiag.getDiagName())
                        .build();
                if(visitDiag.getTimeDiagnosed()!=null){
                    diagnoseDto.setDiseDate(DateUtil.format(visitDiag.getTimeDiagnosed(),"yyyy-MM-dd HH:mm:ss"));
                }
                diagnoseDtos.add(diagnoseDto);
            }
            encounterDto.setDiagnoseDtos(diagnoseDtos);
            List<OrderDto> orderDtos = new ArrayList<>();

            // 如果是门诊处方签名和门诊预结算，从划价单获取数据
            if(params.getTrig_scen().equals(TrigScene.OUTPATIENT_PRESCRIPTION_SIGN.getValue())){
                List<BillEntity> billList = billService.list(
                        new LambdaQueryWrapper<BillEntity>()
                                .eq(BillEntity::getVisitId, params.getVisit_id())
                                .in(BillEntity::getRecipeId, params.getOe_list())
                );
                // 转换成HashMap
                Map<Long, BillEntity> billMap = new HashMap<>();
                for(BillEntity bill: billList){
                    billMap.put(bill.getBseqid(), bill);
                }
                List<BillDetailEntity> billDetailList = billDetailService.list(
                        new LambdaQueryWrapper<BillDetailEntity>()
                                .in(BillDetailEntity::getBseqid,
                                        billList.stream()
                                                .map(BillEntity::getBseqid)
                                                .collect(Collectors.toList()))
                );

                for(BillDetailEntity billDetail: billDetailList){
                    ArticleEntity article = null;
                    if(artMap.containsKey(billDetail.getArtId())){
                        article = artMap.get(billDetail.getArtId());
                    }else {
                        article = articleService.getById(billDetail.getArtId());
                        List<MiSelfpaidPctEntity> miSelfpaidPctList = miSelfpaidPctService.list(
                                new LambdaQueryWrapper<MiSelfpaidPctEntity>()
                                        .eq(MiSelfpaidPctEntity::getArtId, billDetail.getArtId())
                                        .isNull(MiSelfpaidPctEntity::getPersonType).orderByAsc(MiSelfpaidPctEntity::getSelfpaidPct)
                        );
                        String hilistLv = "3";
                        if(!miSelfpaidPctList.isEmpty()){
                            MiSelfpaidPctEntity msp = miSelfpaidPctList.get(0);
                            if(msp.getSelfpaidPct().equals(BigDecimal.ZERO)){
                                hilistLv = "1";
                            }else if(
                                    msp.getSelfpaidPct().compareTo(BigDecimal.ZERO) > 0
                                            && msp.getSelfpaidPct().compareTo(BigDecimal.ONE) < 0
                            ){
                                hilistLv = "2";
                            }
                        }
                        article.setChrgitmLv(hilistLv);
                        artMap.put(billDetail.getArtId(), article);
                    }
                    BillEntity bill = billMap.get(billDetail.getBseqid());
                    OrgDeptEntity applyDept = null;
                    if(deptMap.containsKey(bill.getApplyDeptcode())){
                        applyDept = deptMap.get(bill.getApplyDeptcode());
                    }else{
                        applyDept = orgDeptService.findById(visitItem.getOrgId(), bill.getApplyDeptcode());
                        deptMap.put(bill.getApplyDeptcode(), applyDept);
                    }
                    OrgDeptEntity execDept = null;
                    if(deptMap.containsKey(bill.getExecDeptcode())){
                        execDept = deptMap.get(bill.getExecDeptcode());
                    }else{
                        execDept = orgDeptService.findById(visitItem.getOrgId(), bill.getExecDeptcode());
                        deptMap.put(bill.getExecDeptcode(), execDept);
                    }
                    ClinicianEntity clinician = null;
                    if(clinicianMap.containsKey(bill.getClinicianId())){
                        clinician = clinicianMap.get(bill.getClinicianId());
                    }else {
                        clinician = clinicianService.getById(bill.getClinicianId());
                        clinicianMap.put(bill.getClinicianId(), clinician);
                    }
                    String drordDrProfttl = "235";
                    if(clinician.getQualLevelId()!=null){
                        if(clinician.getQualLevelId()==32){
                            drordDrProfttl = "231";
                        }else if(clinician.getQualLevelId()==31){
                            drordDrProfttl = "232";
                        }else if(clinician.getQualLevelId()==20){
                            drordDrProfttl = "233";
                        }else if(clinician.getQualLevelId()==10){
                            drordDrProfttl = "234";
                        }
                    }

                    FeeTypeEntity feeType = feeTypeService.get(article.getFeeTypeId());
                    if(StringUtil.isBlank(article.getMiCode())){
                        continue;
                    }
                    OrderDto orderDto = OrderDto.builder()
                            .rxId(Convert.toStr(bill.getRecipeId()))
                            .rxno(bill.getRecipeNo())
                            .longDrordFlag("0")
                            .hilistType(feeType.getOtherCode2())
                            .chrgType(feeType.getOtherCode1())
                            // 0=其他，1=出院带药，2=转床，3=观察医嘱
                            .drordBhvr("1")
                            .hilistCode(article.getMiCode())
                            .hilistName(article.getArtName())
                            .hilistLv(article.getChrgitmLv())
                            .hilistPric(BigDecimal.valueOf(billDetail.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .hosplistCode(Convert.toStr(article.getArtId()))
                            .hosplistName(article.getArtName())
                            .cnt(BigDecimal.valueOf(billDetail.getTotal().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .pric(BigDecimal.valueOf(billDetail.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .sumamt(BigDecimal.valueOf(billDetail.getAmount().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .spec(getSpec(article))
                            .specUnt(getUnit(article))
                            .drordBegnDate(DateUtil.format(visit.getTimeAdmission(), "yyyy-MM-dd HH:mm:ss"))
                            .drordDeptCodg(applyDept.getDeptCode())
                            .drordDeptName(applyDept.getDeptName())
                            .drordDrCodg(clinician.getClinicianNo())
                            .drordDrName(clinician.getClinicianName())
                            .drordDrProfttl(drordDrProfttl)
                            .currDrordFlag("0")
                            .build();

                    Map<String, String> extendOrderMap = new HashMap<>();
                    if(article.getCellDoses()!=null){
                        extendOrderMap.put("o_minPacCnt", Convert.toStr(article.getCellDoses().intValue()));
                    }
                    extendOrderMap.put("o_minPrepunt", article.getDoseUnit());
                    extendOrderMap.put("o_minPacUnt", article.getDoseUnit());
                    extendOrderMap.put("o_drugSpec", article.getArtSpec());
                    orderDto.setExtendOrderMap(extendOrderMap);

                    if(StringUtil.isNotBlank(article.getCellUnit())){
                        orderDto.setHilistDosform(article.getCellUnit());
                    }
                    orderDtos.add(orderDto);
                }
            }
            if(params.getTrig_scen().equals(TrigScene.OUTPATIENT_PRE_SETTLEMENT.getValue())){
                 List<BillEntity> billList = billService.list(
                        new LambdaQueryWrapper<BillEntity>()
                                .eq(BillEntity::getVisitId, params.getVisit_id())
                                .eq(BillEntity::getCashId, params.getCash_id())
                );
                // 转换成HashMap
                Map<Long, BillEntity> billMap = new HashMap<>();
                for(BillEntity bill: billList){
                    billMap.put(bill.getBseqid(), bill);
                }
                List<BillDetailEntity> billDetailList = billDetailService.list(
                        new LambdaQueryWrapper<BillDetailEntity>()
                                .in(BillDetailEntity::getBseqid,
                                        billList.stream()
                                                .map(BillEntity::getBseqid)
                                                .collect(Collectors.toList()))
                );

                for(BillDetailEntity billDetail: billDetailList){
                    ArticleEntity article = null;
                    if(artMap.containsKey(billDetail.getArtId())){
                        article = artMap.get(billDetail.getArtId());
                    }else {
                        article = articleService.getById(billDetail.getArtId());
                        List<MiSelfpaidPctEntity> miSelfpaidPctList = miSelfpaidPctService.list(
                                new LambdaQueryWrapper<MiSelfpaidPctEntity>()
                                        .eq(MiSelfpaidPctEntity::getArtId, billDetail.getArtId())
                                        .isNull(MiSelfpaidPctEntity::getPersonType).orderByAsc(MiSelfpaidPctEntity::getSelfpaidPct)
                        );
                        String hilistLv = "3";
                        if(!miSelfpaidPctList.isEmpty()){
                            MiSelfpaidPctEntity msp = miSelfpaidPctList.get(0);
                            if(msp.getSelfpaidPct().equals(BigDecimal.ZERO)){
                                hilistLv = "1";
                            }else if(
                                    msp.getSelfpaidPct().compareTo(BigDecimal.ZERO) > 0
                                            && msp.getSelfpaidPct().compareTo(BigDecimal.ONE) < 0
                            ){
                                hilistLv = "2";
                            }
                        }
                        article.setChrgitmLv(hilistLv);
                        artMap.put(billDetail.getArtId(), article);
                    }
                    BillEntity bill = billMap.get(billDetail.getBseqid());
                    OrgDeptEntity applyDept = null;
                    if(deptMap.containsKey(bill.getApplyDeptcode())){
                        applyDept = deptMap.get(bill.getApplyDeptcode());
                    }else{
                        applyDept = orgDeptService.findById(visitItem.getOrgId(), bill.getApplyDeptcode());
                        deptMap.put(bill.getApplyDeptcode(), applyDept);
                    }
                    OrgDeptEntity execDept = null;
                    if(deptMap.containsKey(bill.getExecDeptcode())){
                        execDept = deptMap.get(bill.getExecDeptcode());
                    }else{
                        execDept = orgDeptService.findById(visitItem.getOrgId(), bill.getExecDeptcode());
                        deptMap.put(bill.getExecDeptcode(), execDept);
                    }
                    ClinicianEntity clinician = null;
                    if(clinicianMap.containsKey(bill.getClinicianId())){
                        clinician = clinicianMap.get(bill.getClinicianId());
                    }else {
                        clinician = clinicianService.getById(bill.getClinicianId());
                        clinicianMap.put(bill.getClinicianId(), clinician);
                    }
                    String drordDrProfttl = "235";
                    if(clinician.getQualLevelId()!=null){
                        if(clinician.getQualLevelId()==32){
                            drordDrProfttl = "231";
                        }else if(clinician.getQualLevelId()==31){
                            drordDrProfttl = "232";
                        }else if(clinician.getQualLevelId()==20){
                            drordDrProfttl = "233";
                        }else if(clinician.getQualLevelId()==10){
                            drordDrProfttl = "234";
                        }
                    }

                    FeeTypeEntity feeType = feeTypeService.get(article.getFeeTypeId());
                    if(StringUtil.isBlank(article.getMiCode())){
                        continue;
                    }
                    OrderDto orderDto = OrderDto.builder()
                            .rxId(Convert.toStr(bill.getRecipeId()))
                            .rxno(bill.getRecipeNo())
                            .longDrordFlag("0")
                            .hilistType(feeType.getOtherCode2())
                            .chrgType(feeType.getOtherCode1())
                            // 0=其他，1=出院带药，2=转床，3=观察医嘱
                            .drordBhvr("1")
                            .hilistCode(article.getMiCode())
                            .hilistName(article.getArtName())
                            .hilistLv(article.getChrgitmLv())
                            .hilistPric(BigDecimal.valueOf(billDetail.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .hosplistCode(Convert.toStr(article.getArtId()))
                            .hosplistName(article.getArtName())
                            .cnt(BigDecimal.valueOf(billDetail.getTotal().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .pric(BigDecimal.valueOf(billDetail.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .sumamt(BigDecimal.valueOf(billDetail.getAmount().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .spec(getSpec(article))
                            .specUnt(getUnit(article))
                            .drordBegnDate(DateUtil.format(visit.getTimeAdmission(), "yyyy-MM-dd HH:mm:ss"))
                            .drordDeptCodg(applyDept.getDeptCode())
                            .drordDeptName(applyDept.getDeptName())
                            .drordDrCodg(clinician.getClinicianNo())
                            .drordDrName(clinician.getClinicianName())
                            .drordDrProfttl(drordDrProfttl)
                            .currDrordFlag("0")
                            .build();

                    Map<String, String> extendOrderMap = new HashMap<>();
                    if(article.getCellDoses()!=null){
                        extendOrderMap.put("o_minPacCnt", Convert.toStr(article.getCellDoses().intValue()));
                    }
                    extendOrderMap.put("o_minPrepunt", article.getDoseUnit());
                    extendOrderMap.put("o_minPacUnt", article.getDoseUnit());
                    extendOrderMap.put("o_drugSpec", article.getArtSpec());
                    orderDto.setExtendOrderMap(extendOrderMap);

                    if(StringUtil.isNotBlank(article.getCellUnit())){
                        orderDto.setHilistDosform(article.getCellUnit());
                    }
                    orderDtos.add(orderDto);
                }
            }

            if(params.getTrig_scen().equals(TrigScene.INPATIENT_PRE_SETTLEMENT.getValue())){
                // 使用医嘱构造OrderDtos
                // 获取医嘱单列表
                List<OrderEntryEntity> orderEntryList = orderEntryService.list(
                        new LambdaQueryWrapper<OrderEntryEntity>()
                                .eq(OrderEntryEntity::getVisitId, visitItem.getVisitId())
                                .orderByAsc(OrderEntryEntity::getOeNo)
                );
                // 获取划价单列表
                List<BillEntity> billList = billService.list(
                        new LambdaQueryWrapper<BillEntity>()
                                .eq(BillEntity::getVisitId, visitItem.getVisitId())
                                .orderByAsc(BillEntity::getBseqid)
                );
                // 转换成HashMap
                Map<Long, BillEntity> billMap = new HashMap<>();
                for(BillEntity bill: billList){
                    billMap.put(bill.getBseqid(), bill);
                }
                for(OrderEntryEntity orderEntry: orderEntryList){
                    List<BillDetailEntity> billDetailList = billDetailService.list(
                            new LambdaQueryWrapper<BillDetailEntity>()
                                    .eq(BillDetailEntity::getOeNo, orderEntry.getOeNo())
                                    .in(BillDetailEntity::getBseqid,
                                            billList.stream()
                                                    .map(BillEntity::getBseqid)
                                                    .collect(Collectors.toList()))
                    );
                    for(BillDetailEntity billDetail: billDetailList){
                        if(orderEntry.getOeTypeId().equals(OeType.SPARE.getValue())){
                            continue;
                        }
                        OrderEntity order = orderService.getById(orderEntry.getOrderId());
                        ArticleEntity article = null;
                        if(artMap.containsKey(billDetail.getArtId())){
                            article = artMap.get(billDetail.getArtId());
                        }else {
                            article = articleService.getById(billDetail.getArtId());
                            List<MiSelfpaidPctEntity> miSelfpaidPctList = miSelfpaidPctService.list(
                                    new LambdaQueryWrapper<MiSelfpaidPctEntity>()
                                            .eq(MiSelfpaidPctEntity::getArtId, billDetail.getArtId())
                                            .isNull(MiSelfpaidPctEntity::getPersonType).orderByAsc(MiSelfpaidPctEntity::getSelfpaidPct)
                            );
                            String hilistLv = "3";
                            if(!miSelfpaidPctList.isEmpty()){
                                MiSelfpaidPctEntity msp = miSelfpaidPctList.get(0);
                                if(msp.getSelfpaidPct().equals(BigDecimal.ZERO)){
                                    hilistLv = "1";
                                }else if(
                                        msp.getSelfpaidPct().compareTo(BigDecimal.ZERO) > 0
                                                && msp.getSelfpaidPct().compareTo(BigDecimal.ONE) < 0
                                ){
                                    hilistLv = "2";
                                }
                            }
                            article.setChrgitmLv(hilistLv);
                            artMap.put(billDetail.getArtId(), article);
                        }
                        BillEntity bill = billMap.get(billDetail.getBseqid());
                        OrgDeptEntity applyDept = null;
                        if(deptMap.containsKey(bill.getApplyDeptcode())){
                            applyDept = deptMap.get(bill.getApplyDeptcode());
                        }else{
                            applyDept = orgDeptService.findById(visitItem.getOrgId(), bill.getApplyDeptcode());
                            deptMap.put(bill.getApplyDeptcode(), applyDept);
                        }
                        OrgDeptEntity execDept = null;
                        if(deptMap.containsKey(bill.getExecDeptcode())){
                            execDept = deptMap.get(bill.getExecDeptcode());
                        }else{
                            execDept = orgDeptService.findById(visitItem.getOrgId(), bill.getExecDeptcode());
                            deptMap.put(bill.getExecDeptcode(), execDept);
                        }
                        ClinicianEntity clinician = null;
                        if(clinicianMap.containsKey(bill.getClinicianId())){
                            clinician = clinicianMap.get(bill.getClinicianId());
                        }else {
                            clinician = clinicianService.getById(bill.getClinicianId());
                            clinicianMap.put(bill.getClinicianId(), clinician);
                        }
                        String drordDrProfttl = "235";
                        if(clinician.getQualLevelId()!=null){
                            if(clinician.getQualLevelId()==32){
                                drordDrProfttl = "231";
                            }else if(clinician.getQualLevelId()==31){
                                drordDrProfttl = "232";
                            }else if(clinician.getQualLevelId()==20){
                                drordDrProfttl = "233";
                            }else if(clinician.getQualLevelId()==10){
                                drordDrProfttl = "234";
                            }
                        }

                        FeeTypeEntity feeType = feeTypeService.get(article.getFeeTypeId());
                        if(StringUtil.isBlank(article.getMiCode())){
                            continue;
                        }
                        OrderDto orderDto = OrderDto.builder()
                                .rxId(visitItem.getVisitId()+"_"+orderEntry.getOeNo())
                                .rxno(visitItem.getVisitId()+"_"+orderEntry.getOeNo())
                                .longDrordFlag(orderEntry.getOeTypeId().equals(OeType.LONG.getValue())?"1":"0")
                                .hilistType(feeType.getOtherCode2())
                                .chrgType(feeType.getOtherCode1())
                                // 0=其他，1=出院带药，2=转床，3=观察医嘱
                                .drordBhvr(order!=null&&order.getOrderTypeId().equals(93)?"1":"0")
                                .hilistCode(article.getMiCode())
                                .hilistName(article.getArtName())
                                .hilistLv(article.getChrgitmLv())
                                .hilistPric(BigDecimal.valueOf(billDetail.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .hosplistCode(Convert.toStr(article.getArtId()))
                                .hosplistName(article.getArtName())
                                .cnt(BigDecimal.valueOf(billDetail.getTotal().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .pric(BigDecimal.valueOf(billDetail.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .sumamt(BigDecimal.valueOf(billDetail.getAmount().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .spec(getSpec(article))
                                .specUnt(getUnit(article))
                                .drordBegnDate(DateUtil.format(orderEntry.getTimeStarted(), "yyyy-MM-dd HH:mm:ss"))
                                .drordDeptCodg(applyDept.getDeptCode())
                                .drordDeptName(applyDept.getDeptName())
                                .drordDrCodg(clinician.getClinicianNo())
                                .drordDrName(clinician.getClinicianName())
                                .drordDrProfttl(drordDrProfttl)
                                .currDrordFlag("0")
                                .build();

                        Map<String, String> extendOrderMap = new HashMap<>();
                        extendOrderMap.put("o_prdDays", Convert.toStr(orderEntry.getPeriodCycles()));
                        if(article.getCellDoses()!=null){
                            extendOrderMap.put("o_minPacCnt", Convert.toStr(article.getCellDoses().intValue()));
                        }
                        extendOrderMap.put("o_minPrepunt", article.getDoseUnit());
                        extendOrderMap.put("o_minPacUnt", article.getDoseUnit());
                        extendOrderMap.put("o_drugSpec", article.getArtSpec());
                        orderDto.setExtendOrderMap(extendOrderMap);

                        if(StringUtil.isNotBlank(article.getCellUnit())){
                            orderDto.setHilistDosform(article.getCellUnit());
                        }
                        if(orderEntry.getTimeStarted()!=null){
                            orderDto.setDrordStopDate(DateUtil.format(orderEntry.getTimeStopped(), "yyyy-MM-dd HH:mm:ss"));
                        }
                        orderDtos.add(orderDto);
                    }
                }

                encounterDto.setOrderDtos(orderDtos);

                encounterDtos.add(encounterDto);
            }

            if(params.getTrig_scen().equals(TrigScene.INPATIENT_ORDER_SIGN.getValue())){
                // 保存当前医嘱
                if(params.getOe_list()!=null){
                    List<Integer> oeList = params.getOe_list();
                    for(Integer oeNo: oeList){
                        OrderEntryEntity orderEntry = orderEntryService.getOne(
                                new LambdaQueryWrapper<OrderEntryEntity>()
                                        .eq(OrderEntryEntity::getVisitId, params.getVisit_id())
                                        .eq(OrderEntryEntity::getOeNo, oeNo)
                                        .ne(OrderEntryEntity::getArtId, 0L)
                        );
                        if(orderEntry==null){
                            continue;
                        }
                        OeExtraEntity oeExtra = oeExtraService.getOne(
                                new LambdaQueryWrapper<OeExtraEntity>()
                                        .eq(OeExtraEntity::getVisitId, params.getVisit_id())
                                        .eq(OeExtraEntity::getOeNo, oeNo)
                        );
                        OrderEntity order = orderService.getById(orderEntry.getOrderId());
                        ArticleEntity article = articleService.getById(orderEntry.getArtId());
                        VisitDeptEntity visitDept = visitDeptService.getById(visitId, orderEntry.getDeptNo());
                        OrgDeptEntity applyDept = orgDeptService.findById(visit.getOrgId(), visitDept.getDeptCode());
                        ClinicianEntity clinician = clinicianService.getById(oeExtra.getApprovedCid()==null?oeExtra.getAppliedCid():oeExtra.getApprovedCid());
//                        Assert.notNull(clinician, "【"+oeExtra.getOeNo()+"】的approvedCid无效【"+oeExtra.getApprovedCid()+"】");
                        String drordDrProfttl = "235";
                        if(clinician.getQualLevelId()!=null){
                            if(clinician.getQualLevelId()==32){
                                drordDrProfttl = "231";
                            }else if(clinician.getQualLevelId()==31){
                                drordDrProfttl = "232";
                            }else if(clinician.getQualLevelId()==20){
                                drordDrProfttl = "233";
                            }else if(clinician.getQualLevelId()==10){
                                drordDrProfttl = "234";
                            }else{
                                drordDrProfttl = "235";
                            }
                        }
                        FeeTypeEntity feeType = feeTypeService.get(article.getFeeTypeId());
                        if(StringUtil.isBlank(article.getMiCode())){
                            continue;
                        }
                        OrgItemPriceEntity orgItemPrice = orgItemPriceService.getById(visit.getOrgId(), article.getArtId());
                        Assert.notNull(article.getPrice(),"【"+article.getArtId()+" "+article.getArtName()+"】价格不能为空");
                        Double cnt = orderEntry.getMealDoses()==null?1:orderEntry.getMealDoses().doubleValue();
                        Double pric = (orgItemPrice==null||orgItemPrice.getUnitPrice()==null)?article.getPrice().doubleValue():orgItemPrice.getUnitPrice().doubleValue();
                        OrderDto orderDto = OrderDto.builder()
                                .rxId(visitId+"_"+orderEntry.getOeNo())
                                .rxno(visitId+"_"+orderEntry.getOeNo())
                                .longDrordFlag(orderEntry.getOeTypeId().equals(OeType.LONG.getValue())?"1":"0")
                                .hilistType(feeType.getOtherCode2())
                                .chrgType(feeType.getOtherCode1())
                                // 0=其他，1=出院带药，2=转床，3=观察医嘱
                                .drordBhvr(order!=null&&order.getOrderTypeId().equals(93)?"1":"0")
                                .hilistCode(article.getMiCode())
                                .hilistName(article.getArtName())
                                .hilistLv(article.getChrgitmLv())
                                .hilistPric(BigDecimal.valueOf(article.getPrice().doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .hosplistCode(Convert.toStr(article.getArtId()))
                                .hosplistName(article.getArtName())
                                .cnt(BigDecimal.valueOf(cnt).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .pric(BigDecimal.valueOf(pric).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .sumamt(BigDecimal.valueOf(cnt*pric).setScale(2, RoundingMode.HALF_UP).doubleValue())
                                .spec(getSpec(article))
                                .specUnt(getUnit(article))
                                .drordBegnDate(DateUtil.format(orderEntry.getTimeStarted(), "yyyy-MM-dd HH:mm:ss"))
                                .drordDeptCodg(applyDept.getDeptCode())
                                .drordDeptName(applyDept.getDeptName())
                                .drordDrCodg(clinician.getClinicianNo())
                                .drordDrName(clinician.getClinicianName())
                                .drordDrProfttl(drordDrProfttl)
                                .currDrordFlag("1")
                                .build();
                        Map<String, String> extendOrderMap = new HashMap<>();
                        orderDto.setExtendOrderMap(extendOrderMap);

                        if(StringUtil.isNotBlank(article.getCellUnit())){
                            orderDto.setHilistDosform(article.getCellUnit());
                        }
                        if(orderEntry.getTimeStopped()!=null){
                            orderDto.setDrordStopDate(DateUtil.format(orderEntry.getTimeStopped(), "yyyy-MM-dd HH:mm:ss"));
                        }
                        orderDtos.add(orderDto);
                        encounterDto.setOrderDtos(orderDtos);

                        encounterDtos.add(encounterDto);
                    }
                }
            }
        }
        patientDto.setEncounterDtos(encounterDtos);
        List<PatientDto> patientDtos = new ArrayList<>();
        patientDtos.add(patientDto);
        TrigSceneEntity trigScene = trigSceneService.getById(params.getTrig_scen());
        RuleAnalysisInfo ruleAnalysisInfo = RuleAnalysisInfo.builder()
                .patientDtos(patientDtos)
                .trigScen(trigScene.getTrigSceneCode())
                .build();
        Input input = Input.builder()
                .data(ruleAnalysisInfo)
                .build();
        return buildMiReq("3101",params, input);
    }

    @SuppressWarnings("unchecked")
    public void after(ApiParams params) {
        // 业务逻辑待实现
        JSONObject miResp = JSONObject.parseObject(params.getMiResp());
        if(miResp.containsKey("output")&&miResp.get("output")!=null){
            JSONObject result = miResp.getJSONObject("output");
            if(result.containsKey("type")&&"error".equals(result.getString("type"))){
                throw new RuntimeException(result.getString("message"));
            }
            params.setMiResp(JSONObject.toJSONString(miResp.get("output")));
        }

    }

    public String getSpec(ArticleEntity article){
        if(StringUtil.isNotBlank(article.getArtSpec())){
            return article.getArtSpec();
        }else{
            return getUnit(article);
        }
    }
    public String getUnit(ArticleEntity article){
        if(StringUtil.isNotBlank(article.getPackUnit())){
            return article.getPackUnit();
        }
        if(StringUtil.isNotBlank(article.getCellUnit())){
            return article.getCellUnit();
        }
        if(StringUtil.isNotBlank(article.getDoseUnit())){
            return article.getDoseUnit();
        }
        throw new RuntimeException("【"+article.getArtId()+" "+article.getArtName()+"】没有有效的单位");
    }
}