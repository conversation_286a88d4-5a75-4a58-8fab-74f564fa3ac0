<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.feiying.med.clinics_wm.dao.DeptArtDao">

    <update id="stockInc">
        update microhis_clinics_wm.t_dept_art set
             total_packs = ifnull(total_packs,0) + ifnull(#{totalPacks},0),
             total_cells = ifnull(total_cells,0) + ifnull(#{totalCells},0)
        where org_id = #{orgId} and dept_code = #{deptCode} and art_id = #{artId}
    </update>
    <update id="stockDec">
        update microhis_clinics_wm.t_dept_art set
             total_packs = ifnull(total_packs,0) - ifnull(#{totalPacks},0),
             total_cells = ifnull(total_cells,0) - ifnull(#{totalCells},0)
        where org_id = #{orgId} and dept_code = #{deptCode} and art_id = #{artId}
    </update>
    <!-- 减少t_dept_art库存，返回更新条数-->
    <update id="stockDecCount">
        update microhis_clinics_wm.t_dept_art set
             total_packs = ifnull(total_packs,0) - ifnull(#{totalPacks},0),
             total_cells = ifnull(total_cells,0) - ifnull(#{totalCells},0)
        where org_id = #{orgId} and dept_code = #{deptCode} and art_id = #{artId}
    </update>
    <update id="updatePurchaseIn">
        update microhis_clinics_wm.t_dept_art set
              Last_Supplemented = #{lastSupplemented},
              Last_Rest_Cells = ifnull(Last_Rest_Cells,0) + ifnull(#{totalCells},0)
        where org_id = #{orgId} and dept_code = #{deptCode} and art_id = #{artId}
    </update>
    <update id="updateReservedCells">
        update microhis_clinics_wm.t_dept_art set
              Reserved_Cells = ifnull(Reserved_Cells,0) + ifnull(#{reservedCells},0)
        where org_id = #{orgId} and dept_code = #{deptCode} and art_id = #{artId}
    </update>
    <sql id="baseDtoSql">
        select t_dept_art.*
             , t_org_dept.Dept_Name
             , t_article.Art_Code
             , t_article.MI_Code
             , t_article.Art_Name
             , t_article.Art_Spec
             , t_article.Producer
             , t_article.Pack_Unit
             , t_article.Cell_Unit
             , t_article.Pack_Cells
             , t_article.Dose_Unit
             , t_article.Cell_Doses
             , t_article.Pack_Material
             , t_article.Dosage_Form
             , t_article.Approval_No
             , t_article.QS_Code1
             , t_article.QS_Code2
             , t_scm_cust.Cust_Name
             , t_org_art.Sale_Disabled
             , t_art_extra.Admin_Cat_ID
             , t_org_formulary.List_Price
             , t_art_subtype.Subtype_ID
             , t_art_subtype.Subtype_Code
             , t_art_subtype.Subtype_Name
        from microhis_clinics_wm.t_dept_art
         left join hip_mdi.t_org_formulary on t_dept_art.Org_ID = t_org_formulary.Org_ID and t_dept_art.Art_ID = t_org_formulary.Art_ID
         left join hip_mdi.t_article on t_dept_art.Art_ID = t_article.Art_ID
         left join microhis_clinics_wm.t_org_art on t_dept_art.Org_ID = t_org_art.Org_ID and t_dept_art.Art_ID = t_org_art.Art_ID
         left join microhis_clinics_wm.t_scm_cust on t_org_art.Cust_ID = t_scm_cust.Cust_ID
         left join hip_mdi.t_art_extra on t_article.Art_ID = t_art_extra.Art_ID
         left join hip_mdi.t_art_subtype on t_article.Subtype_ID = t_art_subtype.Subtype_ID
         left join hip_mdi.t_org_dept on t_org_dept.Org_ID=t_dept_art.Org_ID and t_org_dept.Dept_Code=t_dept_art.Dept_Code
    </sql>
    <select id="queryDtoPage" resultType="cn.feiying.med.clinics_wm.dto.DeptArtDto">
        <include refid="baseDtoSql"></include>
        ${ew.customSqlSegment}
    </select>
    <select id="findDtoById" resultType="cn.feiying.med.clinics_wm.dto.DeptArtDto">
        <include refid="baseDtoSql"></include>
        where t_dept_art.Org_ID = #{orgId} and t_dept_art.Dept_Code = #{deptCode} and t_dept_art.Art_ID = #{artId}
    </select>
    <select id="getArtBatchNoList" resultType="cn.feiying.med.clinics_wm.entity.ArtBatchEntity">
        select t_dept_stock.Art_ID
             , t_art_stockno.Batch_No
        from microhis_clinics_wm.t_dept_stock
             left join microhis_clinics_wm.t_art_stockno on t_dept_stock.Art_ID = t_art_stockno.Art_ID and t_dept_stock.Stock_No = t_art_stockno.Stock_No
        where t_dept_stock.Org_ID = #{orgId} and t_dept_stock.Dept_Code = #{deptCode} and t_dept_stock.Art_ID = #{artId}
    </select>

    <select id="getArtBatchNoListByCountId" resultType="cn.feiying.med.clinics_wm.entity.WmCountDetailEntity">
        select t_dept_stock.Art_ID,
               t_art_stockno.Batch_No,
               sum(ifnull(t_dept_stock.Total_Packs, 0)) as Total_Packs,
               sum(ifnull(t_dept_stock.Total_Cells, 0)) as Total_Cells,
               min(t_dept_art.Rack_No)                  as Rack_No
        from microhis_clinics_wm.t_dept_stock
                 left join microhis_clinics_wm.t_dept_art on t_dept_stock.Org_ID = t_dept_art.Org_ID
            and t_dept_stock.Dept_Code = t_dept_art.Dept_Code
            and t_dept_stock.Art_ID = t_dept_art.Art_ID
                 left join microhis_clinics_wm.t_art_stockno on t_dept_stock.Art_ID = t_art_stockno.Art_ID
            and t_dept_stock.Stock_No = t_art_stockno.Stock_No
        WHERE (t_dept_stock.org_id = #{orgId} AND t_dept_stock.dept_code = #{deptCode} AND t_dept_art.count_id = #{countId})
        group by t_dept_stock.Art_ID, t_art_stockno.Batch_No
    </select>


    <select id="queryAllOrgDeptArtList" resultType="cn.feiying.med.clinics_wm.dto.DeptArtDto">
        select t_dept_art.*
             , t_article.Art_Code
             , t_article.MI_Code
             , t_article.Art_Name
             , t_article.Art_Spec
             , t_article.Producer
             , t_article.Pack_Unit
             , t_article.Cell_Unit
             , t_article.Pack_Cells
             , t_article.Dose_Unit
             , t_article.Cell_Doses
             , t_article.Pack_Material
             , t_article.Dosage_Form
             , t_article.Approval_No
             , t_org.Org_Name
             , t_org_dept.Dept_Name
        from microhis_clinics_wm.t_dept_art
         left join hip_mdi.t_article on t_dept_art.Art_ID = t_article.Art_ID
         left join hip_mdi.t_org on t_dept_art.Org_ID = t_org.Org_ID
         left join hip_mdi.t_org_dept on t_dept_art.Org_ID = t_org_dept.Org_ID and t_dept_art.Dept_Code = t_org_dept.Dept_Code
            ${ew.customSqlSegment}
    </select>
    <select id="calcReqListByWbSeqid" resultType="cn.feiying.med.clinics_wm.dto.DeptStockDto">
        select
            t_dept_stock.*
             , t_article.Art_Code
             , t_article.MI_Code
             , t_article.Art_Name
             , t_article.Art_Spec
             , t_article.Producer
             , t_article.Pack_Unit
             , t_article.Cell_Unit
             , t_article.Pack_Cells
             , t_article.Dose_Unit
             , t_article.Cell_Doses
             , t_article.Pack_Material
             , t_article.Dosage_Form
             , t_article.Approval_No
             , t_wm_bill_detail.Total_Packs as Req_Total_Packs
             , t_wm_bill_detail.Total_Cells as Req_Total_Cells
             , t_art_stockno.Batch_No
        from microhis_clinics_wm.t_wm_bill_detail
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_stock
                           on t_wm_bill.Org_ID = t_dept_stock.Org_ID
                               and t_dept_stock.Dept_Code = #{deptCode}
                               and t_wm_bill_detail.Art_ID = t_dept_stock.Art_ID
                               and t_wm_bill_detail.Stock_No = t_dept_stock.Stock_No
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and
                                                                t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
        where  (
                (t_dept_stock.Total_Packs IS NOT NULL AND t_dept_stock.Total_Packs > 0)
                OR (t_dept_stock.Total_Cells IS NOT NULL AND t_dept_stock.Total_Cells > 0)
            )
            and t_wm_bill.WB_SeqID = #{wbSeqid}
    </select>
    <update id="remakeDeptArt">
            update microhis_clinics_wm.t_dept_art
            inner join (select union_stock.Org_ID,
            union_stock.Dept_Code,
            union_stock.Art_ID,
            sum(union_stock.Total_Packs)    as Total_Packs,
            sum(union_stock.Total_Cells)    as Total_Cells,
            sum(union_stock.Reserved_Cells) as Reserved_Cells
            from (select t_wm_bill.org_id,
            t_wm_bill.Dept_Code,
            t_wm_bill_detail.Art_ID,
            sum(t_wm_bill_detail.Total_Packs)            as Total_Packs,
            sum(t_wm_bill_detail.Total_Cells)            as Total_Cells,
            sum(ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_article.Pack_Cells, 1) +
            ifnull(t_wm_bill_detail.Total_Cells, 0)) as Reserved_Cells                             -- 这段已经能计算Reserved_Cells，减少一次update,提升效率
            from microhis_clinics_wm.t_wm_bill_pending                                                        -- 这里加入pending表只需查找未完结单据，提升效率
            inner join microhis_clinics_wm.t_wm_bill_detail
            on t_wm_bill_pending.WB_SeqID = t_wm_bill_detail.WB_SeqID
            inner join microhis_clinics_wm.t_wm_bill
            on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
            where t_wm_bill.Org_ID =#{orgId}                                                                   -- 下面每段脚本都增加orgId、deptCode、artId筛选，缩小计算范围
            and t_wm_bill.Dept_Code =#{deptCode}
            and t_wm_bill_detail.Art_ID =#{artId}
            and ((t_wm_bill.Bsn_Type in (2, 4) and t_wm_bill.Status in (2, 4)) or
            (t_wm_bill.WMBill_Type_ID in (12, 52) and Status = 1 and Bsn_Type = 3))
            and t_wm_bill.Time_Created >= '2024-12-23'
            group by t_wm_bill.org_id, t_wm_bill.Dept_Code, t_wm_bill_detail.Art_ID
            union all
            select Org_ID,
            Dept_Code,
            Art_ID,
            sum(Total_Packs) as Total_Packs,
            sum(Total_Cells) as Total_Cells,
            0                as Reserved_Cells
            from microhis_clinics_wm.t_dept_stock
            where t_dept_stock.Org_ID = #{orgId}
            and t_dept_stock.Dept_Code = #{deptCode}
            and t_dept_stock.Art_ID = #{artId}
            group by Org_ID, Dept_Code, Art_ID) union_stock
            group by union_stock.Org_ID, union_stock.Dept_Code, union_stock.Art_ID) dept_art_group
        on t_dept_art.Org_ID = dept_art_group.Org_ID and t_dept_art.Dept_Code = dept_art_group.Dept_Code and
            t_dept_art.Art_ID = dept_art_group.Art_ID
            left join hip_mdi.t_article on t_dept_art.Art_ID = t_article.Art_ID
            set t_dept_art.Total_Packs    = dept_art_group.Total_Packs,
                t_dept_art.Total_Cells    = dept_art_group.Total_Cells,
                t_dept_art.Reserved_Cells = dept_art_group.Reserved_Cells
        where t_dept_art.Org_ID = #{orgId}
          and t_dept_art.Dept_Code = #{deptCode}
          and t_dept_art.Art_ID = #{artId};
    </update>
</mapper>